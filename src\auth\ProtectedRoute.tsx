import { Navigate, useLocation, Outlet } from 'react-router-dom';
import { useAuth } from './AuthContext';
import { ReactNode } from 'react';

interface ProtectedRouteProps {
  children?: ReactNode;
}

const ProtectedRoute = ({ children }: ProtectedRouteProps) => {
  const { isAuthenticated } = useAuth();
  const location = useLocation();

  if (!isAuthenticated) {
    // Redirect to the login page, but save the current location they were
    // trying to go to when they were redirected. This allows us to send them
    // to that page after they login, which is a nicer user experience
    // than dropping them off on the home page.
    return <Navigate to="/website/login" state={{ from: location }} replace />;
  }

  // If the user is authenticated, render the child routes
  return children ? <>{children}</> : <Outlet />;
};

export default ProtectedRoute;
