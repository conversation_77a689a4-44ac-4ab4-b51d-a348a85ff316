import React from 'react';
import { useConfig } from '@/config/ConfigContext';
import ConfigurableForm from './ConfigurableForm';

const HomePageForms: React.FC = () => {
  const { config } = useConfig();

  // Check if forms exist in the configuration
  if (!config.forms) {
    return null;
  }

  // Filter forms that have location="home"
  const homeForms = Object.keys(config.forms)
    .filter(formId =>
      config.forms[formId].enabled &&
      config.forms[formId].location === 'home'
    )
    .sort((a, b) => (config.forms[a].displayOrder || 999) - (config.forms[b].displayOrder || 999));

  if (homeForms.length === 0) {
    return null;
  }

  return (
    <div className="space-y-8">
      {homeForms.map(formId => (
        <div key={formId}>
          <ConfigurableForm formId={formId} />
        </div>
      ))}
    </div>
  );
};

export default HomePageForms;
