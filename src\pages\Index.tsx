
import HeroSection from '@/components/HeroSection';
import Section from '@/components/Section';
import { Button } from '@/components/ui/button';
import PropertyCard from '@/components/PropertyCard';
import ProjectCard from '@/components/ProjectCard';
import Testimonial from '@/components/Testimonial';
import { useFeaturedProperties, useFeaturedProjects } from '@/data/dataService';
import { testimonials } from '@/data/testimonials';
import { Link } from 'react-router-dom';
import Layout from '@/components/Layout';
import { useConfig } from '@/config/ConfigContext';
import HomePageForms from '@/components/HomePageForms';
import { Loader2 } from 'lucide-react';

const Index = () => {
  const { config } = useConfig();
  const { featuredProperties, loading: propertiesLoading, error: propertiesError } = useFeaturedProperties(3);
  const { featuredProjects, loading: projectsLoading, error: projectsError } = useFeaturedProjects(3);

  return (
    <Layout>
      {config.homePage.hero.enabled && (
        <HeroSection
          title={config.homePage.hero.title}
          subtitle={config.homePage.hero.subtitle}
          backgroundImage={config.homePage.hero.backgroundImage}
          buttonText={config.homePage.hero.buttonText}
          buttonLink={config.homePage.hero.buttonLink}
        />
      )}

      {/* Display forms with location="home" */}
      {config.homePage.heroForm?.enabled && config.homePage.heroForm?.formId && (
        <Section>
          <HomePageForms />
        </Section>
      )}

      {config.homePage.featuredProperties.enabled && (
        <Section
          title={config.homePage.featuredProperties.title}
          subtitle={config.homePage.featuredProperties.subtitle}
        >
        {propertiesLoading ? (
          <div className="text-center py-16">
            <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
            <p className="text-gray-500">Loading properties...</p>
          </div>
        ) : propertiesError ? (
          <div className="text-center py-8 text-red-500">
            <p>Error loading properties</p>
            <p className="text-sm mt-2">{propertiesError.message}</p>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {featuredProperties.map((property) => (
              <PropertyCard key={property.id} {...property} />
            ))}
          </div>
        )}
        <div className="text-center mt-12">
          <Button asChild variant="outline">
            <Link to="/website/properties">View All Properties</Link>
          </Button>
        </div>
        </Section>
      )}

      {config.homePage.featuredProjects.enabled && (
        <Section
          title={config.homePage.featuredProjects.title}
          subtitle={config.homePage.featuredProjects.subtitle}
          className="bg-gray-50"
        >
        {projectsLoading ? (
          <div className="text-center py-16">
            <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
            <p className="text-gray-500">Loading projects...</p>
          </div>
        ) : projectsError ? (
          <div className="text-center py-8 text-red-500">
            <p>Error loading projects</p>
            <p className="text-sm mt-2">{projectsError.message}</p>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {featuredProjects.map((project) => (
              <ProjectCard key={project.id} {...project} />
            ))}
          </div>
        )}
        <div className="text-center mt-12">
          <Button asChild variant="outline">
            <Link to="/website/projects">View All Projects</Link>
          </Button>
        </div>
        </Section>
      )}

      {config.homePage.whyChooseUs.enabled && (
        <Section
          title={config.homePage.whyChooseUs.title}
          subtitle={config.homePage.whyChooseUs.subtitle}
        >
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          <div className="text-center p-6">
            <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                strokeWidth={1.5}
                stroke="currentColor"
                className="w-8 h-8 text-primary"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  d="M2.25 12l8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25"
                />
              </svg>
            </div>
            <h3 className="text-xl font-semibold mb-2">Premium Properties</h3>
            <p className="text-gray-600">
              Curated selection of luxury properties meeting the highest standards of quality and design.
            </p>
          </div>

          <div className="text-center p-6">
            <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                strokeWidth={1.5}
                stroke="currentColor"
                className="w-8 h-8 text-primary"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  d="M15.75 5.25a3 3 0 013 3m3 0a6 6 0 01-7.029 5.912c-.563-.097-1.159.026-1.563.43L10.5 17.25H8.25v2.25H6v2.25H2.25v-2.818c0-.597.237-1.17.659-1.591l6.499-6.499c.404-.404.527-1 .43-1.563A6 6 0 1121.75 8.25z"
                />
              </svg>
            </div>
            <h3 className="text-xl font-semibold mb-2">Expert Guidance</h3>
            <p className="text-gray-600">
              Professional advice and support throughout your real estate journey from industry experts.
            </p>
          </div>

          <div className="text-center p-6">
            <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                strokeWidth={1.5}
                stroke="currentColor"
                className="w-8 h-8 text-primary"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  d="M11.48 3.499a.562.562 0 011.04 0l2.125 5.111a.563.563 0 00.475.345l5.518.442c.499.04.701.663.321.988l-4.204 3.602a.563.563 0 00-.182.557l1.285 5.385a.562.562 0 01-.84.61l-4.725-2.885a.563.563 0 00-.586 0L6.982 20.54a.562.562 0 01-.84-.61l1.285-5.386a.562.562 0 00-.182-.557l-4.204-3.602a.563.563 0 01.321-.988l5.518-.442a.563.563 0 00.475-.345L11.48 3.5z"
                />
              </svg>
            </div>
            <h3 className="text-xl font-semibold mb-2">Exceptional Service</h3>
            <p className="text-gray-600">
              Client-focused approach ensuring a smooth and satisfying experience from start to finish.
            </p>
          </div>
        </div>
        </Section>
      )}

      {config.homePage.testimonials.enabled && (
        <Section
          title={config.homePage.testimonials.title}
          subtitle={config.homePage.testimonials.subtitle}
          className="bg-gray-50"
        >
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {testimonials.map((testimonial, index) => (
            <Testimonial key={index} {...testimonial} />
          ))}
        </div>
        </Section>
      )}

      {config.homePage.callToAction.enabled && (
        <Section title={config.homePage.callToAction.title}>
          <div className="text-center max-w-2xl mx-auto">
            <p className="text-lg text-gray-600 mb-8">
              {config.homePage.callToAction.text}
            </p>
            <div className="flex flex-col sm:flex-row justify-center gap-4">
              <Button asChild size="lg">
                <Link to={config.homePage.callToAction.primaryButton.link}>
                  {config.homePage.callToAction.primaryButton.text}
                </Link>
              </Button>
              {config.homePage.callToAction.secondaryButton.enabled && (
                <Button asChild size="lg" variant="outline">
                  <Link to={config.homePage.callToAction.secondaryButton.link}>
                    {config.homePage.callToAction.secondaryButton.text}
                  </Link>
                </Button>
              )}
            </div>
          </div>
        </Section>
      )}
    </Layout>
  );
};

export default Index;
