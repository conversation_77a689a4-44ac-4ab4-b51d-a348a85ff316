import React, { useState, useEffect } from 'react';
import { useConfig } from '@/config/ConfigContext';
import { CustomPageConfig, CustomPageComponent } from '@/config/types';
import { useToast } from '@/components/ui/use-toast';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Separator } from '@/components/ui/separator';
import { 
  ArrowLeft,
  Save,
  Eye,
  Plus,
  ExternalLink
} from 'lucide-react';
import { v4 as uuidv4 } from 'uuid';
import PageComponentEditor from './pageComponents/PageComponentEditor';
import ComponentTypeSelector from './pageComponents/ComponentTypeSelector';

interface CustomPageEditorProps {
  pageId: string;
  onBack: () => void;
}

const CustomPageEditor: React.FC<CustomPageEditorProps> = ({ pageId, onBack }) => {
  const { config, updateConfig } = useConfig();
  const { toast } = useToast();
  const [page, setPage] = useState<CustomPageConfig | null>(null);
  const [activeTab, setActiveTab] = useState('general');
  const [isAddingComponent, setIsAddingComponent] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  // Load page data
  useEffect(() => {
    if (config.customPages && config.customPages[pageId]) {
      setPage(config.customPages[pageId]);
    }
    setIsLoading(false);
  }, [config.customPages, pageId]);

  if (isLoading) {
    return <div>Loading...</div>;
  }

  if (!page) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center">
            <h3 className="text-lg font-medium">Page Not Found</h3>
            <p className="text-muted-foreground mt-2">
              The page with ID "{pageId}" could not be found.
            </p>
            <Button onClick={onBack} className="mt-4">
              <ArrowLeft className="h-4 w-4 mr-2" /> Back to Pages
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Handle page property changes
  const handlePageChange = (property: keyof CustomPageConfig, value: any) => {
    setPage(prev => prev ? { ...prev, [property]: value } : null);
  };

  // Handle SEO property changes
  const handleSeoChange = (property: string, value: string) => {
    setPage(prev => {
      if (!prev) return null;
      return {
        ...prev,
        seo: {
          ...prev.seo,
          [property]: value
        }
      };
    });
  };

  // Add a new component
  const handleAddComponent = (type: string) => {
    if (!page) return;
    
    const componentId = `${page.id}_${type}_${uuidv4().substring(0, 8)}`;
    
    let newComponent: CustomPageComponent = {
      type,
      id: componentId,
      title: `New ${type.charAt(0).toUpperCase() + type.slice(1)} Component`,
    };
    
    // Add default properties based on component type
    switch (type) {
      case 'hero':
        newComponent = {
          ...newComponent,
          subtitle: 'Add a subtitle here',
          layout: 'center'
        };
        break;
      case 'text':
        newComponent = {
          ...newComponent,
          content: '<p>Add your content here.</p>',
          layout: 'left'
        };
        break;
      case 'features':
        newComponent = {
          ...newComponent,
          subtitle: 'Add a subtitle here',
          columns: 3,
          items: [
            {
              title: 'Feature 1',
              description: 'Description for feature 1',
              icon: 'star'
            },
            {
              title: 'Feature 2',
              description: 'Description for feature 2',
              icon: 'heart'
            }
          ]
        };
        break;
      case 'cta':
        newComponent = {
          ...newComponent,
          content: 'Add a call to action message here',
          buttonText: 'Click Here',
          buttonLink: '/website/contact',
          backgroundColor: '#f8fafc'
        };
        break;
      case 'accordion':
        newComponent = {
          ...newComponent,
          items: [
            {
              title: 'Question 1',
              content: 'Answer to question 1'
            },
            {
              title: 'Question 2',
              content: 'Answer to question 2'
            }
          ]
        };
        break;
    }
    
    const updatedComponents = [...page.components, newComponent];
    setPage({ ...page, components: updatedComponents });
    setIsAddingComponent(false);
  };

  // Update a component
  const handleUpdateComponent = (index: number, updatedComponent: CustomPageComponent) => {
    if (!page) return;
    
    const updatedComponents = [...page.components];
    updatedComponents[index] = updatedComponent;
    setPage({ ...page, components: updatedComponents });
  };

  // Remove a component
  const handleRemoveComponent = (index: number) => {
    if (!page) return;
    
    const updatedComponents = page.components.filter((_, i) => i !== index);
    setPage({ ...page, components: updatedComponents });
  };

  // Move a component up
  const handleMoveComponentUp = (index: number) => {
    if (!page || index === 0) return;
    
    const updatedComponents = [...page.components];
    const temp = updatedComponents[index];
    updatedComponents[index] = updatedComponents[index - 1];
    updatedComponents[index - 1] = temp;
    
    setPage({ ...page, components: updatedComponents });
  };

  // Move a component down
  const handleMoveComponentDown = (index: number) => {
    if (!page || index === page.components.length - 1) return;
    
    const updatedComponents = [...page.components];
    const temp = updatedComponents[index];
    updatedComponents[index] = updatedComponents[index + 1];
    updatedComponents[index + 1] = temp;
    
    setPage({ ...page, components: updatedComponents });
  };

  // Save page changes
  const handleSavePage = () => {
    if (!page) return;
    
    // Validate page
    if (!page.title.trim()) {
      toast({
        title: 'Validation Error',
        description: 'Page title is required',
        variant: 'destructive'
      });
      return;
    }
    
    if (!page.slug.trim()) {
      toast({
        title: 'Validation Error',
        description: 'Page slug is required',
        variant: 'destructive'
      });
      return;
    }
    
    // Check if slug is unique (except for this page)
    const slugExists = Object.values(config.customPages || {}).some(
      p => p.id !== page.id && p.slug === page.slug
    );
    
    if (slugExists) {
      toast({
        title: 'Validation Error',
        description: 'A page with this slug already exists',
        variant: 'destructive'
      });
      return;
    }
    
    // Update the config
    const updatedPages = {
      ...config.customPages,
      [pageId]: page
    };
    
    const updatedConfig = {
      ...config,
      customPages: updatedPages
    };
    
    updateConfig(updatedConfig);
    
    toast({
      title: 'Page Saved',
      description: 'Your page has been saved successfully'
    });
  };

  // View the page in a new tab
  const handleViewPage = () => {
    window.open(`/website/page/${page.slug}`, '_blank');
  };

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <div className="flex items-center gap-2">
          <Button onClick={onBack} variant="outline" size="icon" title="Back to Pages">
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <h2 className="text-2xl font-bold">Edit Page: {page?.title}</h2>
        </div>
        <div className="flex gap-2">
          <Button onClick={handleViewPage} variant="outline">
            <ExternalLink className="h-4 w-4 mr-2" /> View Page
          </Button>
          <Button onClick={handleSavePage} variant="default">
            <Save className="h-4 w-4 mr-2" /> Save Changes
          </Button>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="mb-4">
          <TabsTrigger value="general">General</TabsTrigger>
          <TabsTrigger value="components">Components ({page?.components.length || 0})</TabsTrigger>
          <TabsTrigger value="seo">SEO</TabsTrigger>
        </TabsList>

        {/* General Tab */}
        <TabsContent value="general">
          <Card>
            <CardHeader>
              <CardTitle>Page Settings</CardTitle>
              <CardDescription>Basic information about your page</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="page-title">Page Title</Label>
                  <Input
                    id="page-title"
                    value={page.title}
                    onChange={(e) => handlePageChange('title', e.target.value)}
                    placeholder="Page Title"
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="page-slug">Page Slug (URL)</Label>
                  <Input
                    id="page-slug"
                    value={page.slug}
                    onChange={(e) => handlePageChange('slug', e.target.value)}
                    placeholder="page-slug"
                    required
                  />
                  <p className="text-xs text-muted-foreground">
                    This will be used in the URL: /website/page/<strong>{page.slug}</strong>
                  </p>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="page-description">Description</Label>
                <Textarea
                  id="page-description"
                  value={page.description || ''}
                  onChange={(e) => handlePageChange('description', e.target.value)}
                  placeholder="Brief description of this page"
                  rows={3}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="flex items-center space-x-2">
                  <Switch
                    id="page-enabled"
                    checked={page.enabled}
                    onCheckedChange={(checked) => handlePageChange('enabled', checked)}
                  />
                  <Label htmlFor="page-enabled">Page Enabled</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Switch
                    id="page-navigation"
                    checked={page.showInNavigation}
                    onCheckedChange={(checked) => handlePageChange('showInNavigation', checked)}
                  />
                  <Label htmlFor="page-navigation">Show in Navigation</Label>
                </div>
              </div>

              {page.showInNavigation && (
                <div className="space-y-2">
                  <Label htmlFor="page-order">Navigation Order</Label>
                  <Input
                    id="page-order"
                    type="number"
                    value={page.navigationOrder || ''}
                    onChange={(e) => handlePageChange('navigationOrder', parseInt(e.target.value) || undefined)}
                    min={1}
                    placeholder="1"
                  />
                  <p className="text-xs text-muted-foreground">
                    Lower numbers appear first in the navigation menu
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Components Tab */}
        <TabsContent value="components">
          <Card>
            <CardHeader>
              <CardTitle>Page Components</CardTitle>
              <CardDescription>Add, edit, or remove components from your page</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {page.components.length === 0 ? (
                <div className="text-center p-8 border border-dashed rounded-md">
                  <p className="text-muted-foreground mb-4">No components added yet</p>
                  <Button onClick={() => setIsAddingComponent(true)}>Add Your First Component</Button>
                </div>
              ) : (
                <div className="space-y-6">
                  {page.components.map((component, index) => (
                    <PageComponentEditor
                      key={component.id}
                      component={component}
                      index={index}
                      isFirst={index === 0}
                      isLast={index === page.components.length - 1}
                      onUpdate={(updatedComponent) => handleUpdateComponent(index, updatedComponent)}
                      onRemove={() => handleRemoveComponent(index)}
                      onMoveUp={() => handleMoveComponentUp(index)}
                      onMoveDown={() => handleMoveComponentDown(index)}
                    />
                  ))}
                </div>
              )}

              <Button
                type="button"
                onClick={() => setIsAddingComponent(true)}
                className="w-full mt-4"
                variant="outline"
              >
                <Plus className="h-4 w-4 mr-2" /> Add Component
              </Button>

              {isAddingComponent && (
                <ComponentTypeSelector
                  onSelect={handleAddComponent}
                  onCancel={() => setIsAddingComponent(false)}
                />
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* SEO Tab */}
        <TabsContent value="seo">
          <Card>
            <CardHeader>
              <CardTitle>SEO Settings</CardTitle>
              <CardDescription>Optimize your page for search engines</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="seo-title">SEO Title</Label>
                <Input
                  id="seo-title"
                  value={page.seo?.title || ''}
                  onChange={(e) => handleSeoChange('title', e.target.value)}
                  placeholder={`${page.title} | ${config.siteName}`}
                />
                <p className="text-xs text-muted-foreground">
                  Leave blank to use the default: "{page.title} | {config.siteName}"
                </p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="seo-description">Meta Description</Label>
                <Textarea
                  id="seo-description"
                  value={page.seo?.description || ''}
                  onChange={(e) => handleSeoChange('description', e.target.value)}
                  placeholder="Brief description for search engines"
                  rows={3}
                />
                <p className="text-xs text-muted-foreground">
                  Recommended length: 150-160 characters
                </p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="seo-keywords">Meta Keywords</Label>
                <Input
                  id="seo-keywords"
                  value={page.seo?.keywords || ''}
                  onChange={(e) => handleSeoChange('keywords', e.target.value)}
                  placeholder="keyword1, keyword2, keyword3"
                />
                <p className="text-xs text-muted-foreground">
                  Comma-separated list of keywords
                </p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="seo-image">OG Image URL</Label>
                <Input
                  id="seo-image"
                  value={page.seo?.ogImage || ''}
                  onChange={(e) => handleSeoChange('ogImage', e.target.value)}
                  placeholder="https://example.com/image.jpg"
                />
                <p className="text-xs text-muted-foreground">
                  Image to display when sharing on social media
                </p>
              </div>
            </CardContent>
            <CardFooter className="flex justify-end">
              <Button onClick={handleSavePage}>
                <Save className="h-4 w-4 mr-2" /> Save Changes
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default CustomPageEditor;
