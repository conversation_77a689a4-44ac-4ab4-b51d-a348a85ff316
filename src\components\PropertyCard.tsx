
import { Card, CardContent, CardFooter } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { MapPin } from 'lucide-react';
import { Link } from 'react-router-dom';

export interface PropertyProps {
  id: string;
  title: string;
  location: string;
  price: string;
  beds: number;
  baths: number;
  sqft: number;
  imageUrl: string;
  status: 'For Sale' | 'For Rent' | 'Sold';
}

const PropertyCard = ({
  id,
  title,
  location,
  price,
  beds,
  baths,
  sqft,
  imageUrl,
  status,
}: PropertyProps) => {
  return (
    <Card className="overflow-hidden h-full transition-transform duration-300 hover:shadow-lg">
      <div className="relative">
        <Link to={`/website/properties/${id}`}>
          <img
            src={imageUrl}
            alt={title}
            className="w-full h-56 object-cover"
          />
        </Link>
        <Badge
          className={`absolute top-3 right-3 ${
            status === 'For Sale' 
              ? 'bg-primary' 
              : status === 'For Rent' 
                ? 'bg-blue-500' 
                : 'bg-gray-500'
          }`}
        >
          {status}
        </Badge>
      </div>
      
      <CardContent className="pt-4">
        <Link to={`/website/properties/${id}`}>
          <h3 className="text-lg font-semibold line-clamp-1 hover:text-primary transition-colors">
            {title}
          </h3>
        </Link>
        <div className="flex items-center mt-1 text-gray-600">
          <MapPin size={16} className="mr-1" />
          <span className="text-sm">{location}</span>
        </div>
        <p className="mt-2 text-xl font-bold text-primary">{price}</p>
        
        <div className="mt-3 flex justify-between text-sm text-gray-600">
          <div className="flex items-center">
            <span className="font-medium">{beds}</span>
            <span className="ml-1">Beds</span>
          </div>
          <div className="flex items-center">
            <span className="font-medium">{baths}</span>
            <span className="ml-1">Baths</span>
          </div>
          <div className="flex items-center">
            <span className="font-medium">{sqft}</span>
            <span className="ml-1">Sq Ft</span>
          </div>
        </div>
      </CardContent>
      
      <CardFooter className="pt-0">
        <Link 
          to={`/website/properties/${id}`} 
          className="text-primary font-medium text-sm hover:underline"
        >
          View Details
        </Link>
      </CardFooter>
    </Card>
  );
};

export default PropertyCard;
