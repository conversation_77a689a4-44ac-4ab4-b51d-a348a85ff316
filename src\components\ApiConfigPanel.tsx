import React, { useState } from 'react';
import { useConfig } from '@/config/ConfigContext';
import { ApiConfig } from '@/config/types';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { useToast } from '@/components/ui/use-toast';
import { Trash2, Plus, Save, RefreshCw } from 'lucide-react';
import { clearApiCache } from '@/services/apiService';

interface ApiConfigPanelProps {
  type: 'properties' | 'projects';
}

const ApiConfigPanel: React.FC<ApiConfigPanelProps> = ({ type }) => {
  const { config, updateConfig } = useConfig();
  const { toast } = useToast();
  
  // Get the appropriate API config based on type
  const apiConfig = type === 'properties' 
    ? config.propertiesPage.api 
    : config.projectsPage.api;
  
  // Create a copy of the API config for editing
  const [editedConfig, setEditedConfig] = useState<ApiConfig>(
    apiConfig || {
      enabled: false,
      url: '',
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
      responseMapping: {
        items: 'data',
        id: 'id',
        title: 'title',
        description: 'description',
        image: 'image',
        ...(type === 'properties' 
          ? { price: 'price', location: 'location', features: 'features' }
          : { status: 'status', location: 'location', features: 'features' })
      },
      cacheDuration: 30
    }
  );
  
  // Handle changes to the API config
  const handleConfigChange = (field: keyof ApiConfig, value: any) => {
    setEditedConfig(prev => ({
      ...prev,
      [field]: value
    }));
  };
  
  // Handle changes to response mapping
  const handleMappingChange = (field: string, value: string) => {
    setEditedConfig(prev => ({
      ...prev,
      responseMapping: {
        ...prev.responseMapping,
        [field]: value
      }
    }));
  };
  
  // Handle changes to headers
  const handleHeaderChange = (index: number, field: 'key' | 'value', value: string) => {
    const headers = { ...editedConfig.headers } || {};
    const keys = Object.keys(headers);
    
    if (field === 'key') {
      const oldKey = keys[index];
      const oldValue = headers[oldKey];
      
      // Remove old key and add new one
      delete headers[oldKey];
      headers[value] = oldValue;
    } else {
      const key = keys[index];
      headers[key] = value;
    }
    
    setEditedConfig(prev => ({
      ...prev,
      headers
    }));
  };
  
  // Add a new header
  const addHeader = () => {
    const headers = { ...editedConfig.headers } || {};
    headers[`header${Object.keys(headers).length + 1}`] = '';
    
    setEditedConfig(prev => ({
      ...prev,
      headers
    }));
  };
  
  // Remove a header
  const removeHeader = (key: string) => {
    const headers = { ...editedConfig.headers } || {};
    delete headers[key];
    
    setEditedConfig(prev => ({
      ...prev,
      headers
    }));
  };
  
  // Save the API config
  const saveConfig = () => {
    // Create a new config object
    const newConfig = { ...config };
    
    if (type === 'properties') {
      newConfig.propertiesPage = {
        ...newConfig.propertiesPage,
        api: editedConfig
      };
    } else {
      newConfig.projectsPage = {
        ...newConfig.projectsPage,
        api: editedConfig
      };
    }
    
    // Update the config
    updateConfig(newConfig);
    
    // Clear the API cache
    clearApiCache();
    
    // Show a success message
    toast({
      title: 'API Configuration Saved',
      description: `The ${type} API configuration has been updated.`,
    });
  };
  
  // Reset the cache
  const resetCache = () => {
    clearApiCache();
    
    toast({
      title: 'Cache Cleared',
      description: 'The API cache has been cleared. New data will be fetched on the next request.',
    });
  };
  
  return (
    <Card>
      <CardHeader>
        <CardTitle>API Configuration for {type === 'properties' ? 'Properties' : 'Projects'}</CardTitle>
        <CardDescription>
          Configure how to fetch {type} data from an external API
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="general">
          <TabsList className="mb-4">
            <TabsTrigger value="general">General</TabsTrigger>
            <TabsTrigger value="mapping">Data Mapping</TabsTrigger>
            <TabsTrigger value="headers">Headers</TabsTrigger>
          </TabsList>
          
          <TabsContent value="general">
            <div className="space-y-4">
              <div className="flex items-center space-x-2">
                <Switch
                  id={`${type}-api-enabled`}
                  checked={editedConfig.enabled}
                  onCheckedChange={(checked) => handleConfigChange('enabled', checked)}
                />
                <Label htmlFor={`${type}-api-enabled`}>Enable API Integration</Label>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor={`${type}-api-url`}>API Endpoint URL</Label>
                <Input
                  id={`${type}-api-url`}
                  value={editedConfig.url}
                  onChange={(e) => handleConfigChange('url', e.target.value)}
                  placeholder="https://api.example.com/data"
                  disabled={!editedConfig.enabled}
                />
                <p className="text-xs text-muted-foreground">
                  The URL of the API endpoint that returns {type} data
                </p>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor={`${type}-api-method`}>HTTP Method</Label>
                <Select
                  value={editedConfig.method}
                  onValueChange={(value) => handleConfigChange('method', value)}
                  disabled={!editedConfig.enabled}
                >
                  <SelectTrigger id={`${type}-api-method`}>
                    <SelectValue placeholder="Select method" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="GET">GET</SelectItem>
                    <SelectItem value="POST">POST</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              {editedConfig.method === 'POST' && (
                <div className="space-y-2">
                  <Label htmlFor={`${type}-api-body`}>Request Body (JSON)</Label>
                  <Textarea
                    id={`${type}-api-body`}
                    value={editedConfig.bodyParams ? JSON.stringify(editedConfig.bodyParams, null, 2) : ''}
                    onChange={(e) => {
                      try {
                        const bodyParams = e.target.value ? JSON.parse(e.target.value) : undefined;
                        handleConfigChange('bodyParams', bodyParams);
                      } catch (error) {
                        // Don't update if JSON is invalid
                      }
                    }}
                    placeholder="{}"
                    disabled={!editedConfig.enabled}
                    className="font-mono"
                    rows={5}
                  />
                  <p className="text-xs text-muted-foreground">
                    JSON data to send in the request body (for POST requests)
                  </p>
                </div>
              )}
              
              <div className="space-y-2">
                <Label htmlFor={`${type}-api-cache`}>Cache Duration (minutes)</Label>
                <Input
                  id={`${type}-api-cache`}
                  type="number"
                  value={editedConfig.cacheDuration || 30}
                  onChange={(e) => handleConfigChange('cacheDuration', parseInt(e.target.value) || 30)}
                  min={0}
                  max={1440}
                  disabled={!editedConfig.enabled}
                />
                <p className="text-xs text-muted-foreground">
                  How long to cache API responses (0 for no caching)
                </p>
              </div>
            </div>
          </TabsContent>
          
          <TabsContent value="mapping">
            <div className="space-y-4">
              <p className="text-sm text-muted-foreground mb-4">
                Map API response fields to the application's data model. The "items" field should point to the array of items in the response.
              </p>
              
              <div className="space-y-2">
                <Label htmlFor={`${type}-mapping-items`}>Items Path</Label>
                <Input
                  id={`${type}-mapping-items`}
                  value={editedConfig.responseMapping.items}
                  onChange={(e) => handleMappingChange('items', e.target.value)}
                  placeholder="data"
                  disabled={!editedConfig.enabled}
                />
                <p className="text-xs text-muted-foreground">
                  JSON path to the array of items (e.g., "data" or "results" or "data.items")
                </p>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor={`${type}-mapping-id`}>ID Field</Label>
                  <Input
                    id={`${type}-mapping-id`}
                    value={editedConfig.responseMapping.id}
                    onChange={(e) => handleMappingChange('id', e.target.value)}
                    placeholder="id"
                    disabled={!editedConfig.enabled}
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor={`${type}-mapping-title`}>Title Field</Label>
                  <Input
                    id={`${type}-mapping-title`}
                    value={editedConfig.responseMapping.title}
                    onChange={(e) => handleMappingChange('title', e.target.value)}
                    placeholder="title"
                    disabled={!editedConfig.enabled}
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor={`${type}-mapping-description`}>Description Field</Label>
                  <Input
                    id={`${type}-mapping-description`}
                    value={editedConfig.responseMapping.description}
                    onChange={(e) => handleMappingChange('description', e.target.value)}
                    placeholder="description"
                    disabled={!editedConfig.enabled}
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor={`${type}-mapping-image`}>Image Field</Label>
                  <Input
                    id={`${type}-mapping-image`}
                    value={editedConfig.responseMapping.image}
                    onChange={(e) => handleMappingChange('image', e.target.value)}
                    placeholder="image_url"
                    disabled={!editedConfig.enabled}
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor={`${type}-mapping-location`}>Location Field</Label>
                  <Input
                    id={`${type}-mapping-location`}
                    value={editedConfig.responseMapping.location}
                    onChange={(e) => handleMappingChange('location', e.target.value)}
                    placeholder="location"
                    disabled={!editedConfig.enabled}
                  />
                </div>
                
                {type === 'properties' ? (
                  <div className="space-y-2">
                    <Label htmlFor={`${type}-mapping-price`}>Price Field</Label>
                    <Input
                      id={`${type}-mapping-price`}
                      value={editedConfig.responseMapping.price}
                      onChange={(e) => handleMappingChange('price', e.target.value)}
                      placeholder="price"
                      disabled={!editedConfig.enabled}
                    />
                  </div>
                ) : (
                  <div className="space-y-2">
                    <Label htmlFor={`${type}-mapping-status`}>Status Field</Label>
                    <Input
                      id={`${type}-mapping-status`}
                      value={editedConfig.responseMapping.status}
                      onChange={(e) => handleMappingChange('status', e.target.value)}
                      placeholder="status"
                      disabled={!editedConfig.enabled}
                    />
                  </div>
                )}
                
                {type === 'properties' && (
                  <>
                    <div className="space-y-2">
                      <Label htmlFor={`${type}-mapping-beds`}>Beds Field</Label>
                      <Input
                        id={`${type}-mapping-beds`}
                        value={editedConfig.responseMapping.beds}
                        onChange={(e) => handleMappingChange('beds', e.target.value)}
                        placeholder="beds"
                        disabled={!editedConfig.enabled}
                      />
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor={`${type}-mapping-baths`}>Baths Field</Label>
                      <Input
                        id={`${type}-mapping-baths`}
                        value={editedConfig.responseMapping.baths}
                        onChange={(e) => handleMappingChange('baths', e.target.value)}
                        placeholder="baths"
                        disabled={!editedConfig.enabled}
                      />
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor={`${type}-mapping-sqft`}>Square Feet Field</Label>
                      <Input
                        id={`${type}-mapping-sqft`}
                        value={editedConfig.responseMapping.sqft}
                        onChange={(e) => handleMappingChange('sqft', e.target.value)}
                        placeholder="square_feet"
                        disabled={!editedConfig.enabled}
                      />
                    </div>
                  </>
                )}
                
                {type === 'projects' && (
                  <>
                    <div className="space-y-2">
                      <Label htmlFor={`${type}-mapping-units`}>Units Field</Label>
                      <Input
                        id={`${type}-mapping-units`}
                        value={editedConfig.responseMapping.units}
                        onChange={(e) => handleMappingChange('units', e.target.value)}
                        placeholder="units"
                        disabled={!editedConfig.enabled}
                      />
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor={`${type}-mapping-completion`}>Completion Date Field</Label>
                      <Input
                        id={`${type}-mapping-completion`}
                        value={editedConfig.responseMapping.completionDate}
                        onChange={(e) => handleMappingChange('completionDate', e.target.value)}
                        placeholder="completion_date"
                        disabled={!editedConfig.enabled}
                      />
                    </div>
                  </>
                )}
              </div>
            </div>
          </TabsContent>
          
          <TabsContent value="headers">
            <div className="space-y-4">
              <p className="text-sm text-muted-foreground mb-4">
                Configure HTTP headers to send with the API request
              </p>
              
              {editedConfig.headers && Object.keys(editedConfig.headers).length > 0 ? (
                <div className="space-y-2">
                  {Object.entries(editedConfig.headers).map(([key, value], index) => (
                    <div key={index} className="flex items-center space-x-2">
                      <Input
                        value={key}
                        onChange={(e) => handleHeaderChange(index, 'key', e.target.value)}
                        placeholder="Header Name"
                        disabled={!editedConfig.enabled}
                        className="flex-1"
                      />
                      <Input
                        value={value}
                        onChange={(e) => handleHeaderChange(index, 'value', e.target.value)}
                        placeholder="Value"
                        disabled={!editedConfig.enabled}
                        className="flex-1"
                      />
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => removeHeader(key)}
                        disabled={!editedConfig.enabled}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center p-4 border border-dashed rounded-md">
                  <p className="text-muted-foreground">No headers configured</p>
                </div>
              )}
              
              <Button
                variant="outline"
                size="sm"
                onClick={addHeader}
                disabled={!editedConfig.enabled}
                className="mt-2"
              >
                <Plus className="h-4 w-4 mr-2" /> Add Header
              </Button>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
      <CardFooter className="flex justify-between">
        <Button
          variant="outline"
          onClick={resetCache}
          disabled={!editedConfig.enabled}
        >
          <RefreshCw className="h-4 w-4 mr-2" /> Clear Cache
        </Button>
        <Button onClick={saveConfig}>
          <Save className="h-4 w-4 mr-2" /> Save Configuration
        </Button>
      </CardFooter>
    </Card>
  );
};

export default ApiConfigPanel;
