import { WebsiteConfig } from '../config/types';
import { defaultConfig } from '../config/defaultConfig';

/**
 * Service for handling API interactions related to configuration
 */
export const configApiService = {
  /**
   * Fetch configuration from an API endpoint
   * @param url The API endpoint URL
   * @returns Promise resolving to the configuration
   */
  async fetchConfig(url: string): Promise<WebsiteConfig> {
    try {
      const response = await fetch(url);
      
      if (!response.ok) {
        throw new Error(`API error: ${response.status} ${response.statusText}`);
      }
      
      const data = await response.json();
      return data as WebsiteConfig;
    } catch (error) {
      console.error('Error fetching configuration from API:', error);
      throw error;
    }
  },
  
  /**
   * Validate that the provided configuration has the required structure
   * @param config The configuration to validate
   * @returns An object with validation result and any error messages
   */
  validateConfig(config: any): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];
    
    // Check if config is an object
    if (!config || typeof config !== 'object') {
      errors.push('Configuration must be an object');
      return { isValid: false, errors };
    }
    
    // Check required top-level properties
    const requiredProps = ['siteName', 'logoText', 'forms', 'navigation'];
    for (const prop of requiredProps) {
      if (!(prop in config)) {
        errors.push(`Missing required property: ${prop}`);
      }
    }
    
    // Check page configurations
    const requiredPages = ['homePage', 'aboutPage', 'contactPage', 'propertiesPage', 'projectsPage'];
    for (const page of requiredPages) {
      if (!(page in config)) {
        errors.push(`Missing required page configuration: ${page}`);
      } else if (!('enabled' in config[page])) {
        errors.push(`Missing 'enabled' property in ${page} configuration`);
      }
    }
    
    // Check forms structure
    if (config.forms && typeof config.forms === 'object') {
      Object.keys(config.forms).forEach(formId => {
        const form = config.forms[formId];
        if (!form.id) {
          errors.push(`Form ${formId} is missing 'id' property`);
        }
        if (!form.fields || !Array.isArray(form.fields)) {
          errors.push(`Form ${formId} is missing 'fields' array`);
        }
      });
    }
    
    return { 
      isValid: errors.length === 0,
      errors 
    };
  },
  
  /**
   * Merge the API configuration with the default configuration to ensure all required properties exist
   * @param apiConfig The configuration from the API
   * @returns A complete configuration with all required properties
   */
  mergeWithDefaultConfig(apiConfig: Partial<WebsiteConfig>): WebsiteConfig {
    // Create a deep copy of the default config
    const mergedConfig = JSON.parse(JSON.stringify(defaultConfig)) as WebsiteConfig;
    
    // Recursively merge the API config into the default config
    const deepMerge = (target: any, source: any) => {
      for (const key in source) {
        if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {
          // If the key doesn't exist in target, create it
          if (!target[key]) target[key] = {};
          deepMerge(target[key], source[key]);
        } else {
          // For arrays and primitive values, replace the value
          target[key] = source[key];
        }
      }
      return target;
    };
    
    return deepMerge(mergedConfig, apiConfig);
  }
};
