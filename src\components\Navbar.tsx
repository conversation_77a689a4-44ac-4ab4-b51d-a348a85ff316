
import { useState, useEffect } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Menu, X } from 'lucide-react';
import { useConfig } from '@/config/ConfigContext';

const Navbar = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [scrolled, setScrolled] = useState(false);
  const location = useLocation();
  const { config } = useConfig();

  const toggleMenu = () => setIsOpen(!isOpen);
  const closeMenu = () => setIsOpen(false);

  // Get navigation links from config and filter based on page enabled status
  const navLinks = config.navigation.links.filter(link => {
    if (!link.enabled) return false;

    // Check if the corresponding page is enabled
    if (link.path.includes('/properties') && !config.propertiesPage.enabled) return false;
    if (link.path.includes('/projects') && !config.projectsPage.enabled) return false;
    if (link.path.includes('/about') && !config.aboutPage.enabled) return false;
    if (link.path.includes('/contact') && !config.contactPage.enabled) return false;

    return true;
  });

  // Add custom pages to navigation if they are enabled and set to show in navigation
  const customPages = config.customPages || {};
  const customPageLinks = Object.values(customPages)
    .filter(page => page.enabled && page.showInNavigation)
    .sort((a, b) => (a.navigationOrder || 999) - (b.navigationOrder || 999))
    .map(page => ({
      name: page.title,
      path: `/website/page/${page.slug}`,
      enabled: true
    }));

  // Combine standard and custom page links
  const allNavLinks = [...navLinks, ...customPageLinks];

  // Handle scroll effect
  useEffect(() => {
    const handleScroll = () => {
      if (window.scrollY > 10) {
        setScrolled(true);
      } else {
        setScrolled(false);
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);

  return (
    <nav
      className={`fixed top-0 left-0 w-full z-50 transition-all duration-300 ${
        scrolled ? 'bg-white shadow-md py-2' : 'bg-transparent py-4'
      }`}
    >
      <div className="container mx-auto px-4 flex justify-between items-center">
        {/* Logo */}
        <Link to="/website" className="text-2xl font-bold text-primary flex items-center">
          {config.headerLogoImage ? (
            <img
              src={config.headerLogoImage}
              alt={config.logoText}
              className="h-10 mr-2"
            />
          ) : null}
          {config.logoText}
        </Link>

        {/* Desktop Navigation */}
        <div className="hidden md:flex items-center space-x-8">
          <div className="flex space-x-6">
            {allNavLinks.map((link) => (
              <Link
                key={link.name}
                to={link.path}
                className={`font-medium hover:text-primary transition-colors ${
                  location.pathname === link.path
                    ? 'text-primary'
                    : 'text-gray-700'
                }`}
              >
                {link.name}
              </Link>
            ))}
          </div>

          {config.navigation.ctaButton.enabled &&
           // Only show CTA if the target page is enabled
           (!config.navigation.ctaButton.link.includes('/contact') || config.contactPage.enabled) && (
            <Button asChild>
              <Link to={config.navigation.ctaButton.link}>
                {config.navigation.ctaButton.text}
              </Link>
            </Button>
          )}
        </div>

        {/* Mobile Menu Button */}
        <div className="md:hidden">
          <Button variant="ghost" size="icon" onClick={toggleMenu}>
            {isOpen ? <X /> : <Menu />}
          </Button>
        </div>
      </div>

      {/* Mobile Navigation */}
      {isOpen && (
        <div className="fixed inset-0 bg-white z-40 pt-20 px-4 md:hidden animate-fade-in">
          <div className="flex flex-col space-y-4">
            {allNavLinks.map((link) => (
              <Link
                key={link.name}
                to={link.path}
                className={`text-lg font-medium py-2 hover:text-primary transition-colors ${
                  location.pathname === link.path
                    ? 'text-primary'
                    : 'text-gray-700'
                }`}
                onClick={closeMenu}
              >
                {link.name}
              </Link>
            ))}
            <div className="pt-4">
              {config.navigation.ctaButton.enabled &&
               // Only show CTA if the target page is enabled
               (!config.navigation.ctaButton.link.includes('/contact') || config.contactPage.enabled) && (
                <Button asChild className="w-full" onClick={closeMenu}>
                  <Link to={config.navigation.ctaButton.link}>
                    {config.navigation.ctaButton.text}
                  </Link>
                </Button>
              )}
            </div>
          </div>
        </div>
      )}
    </nav>
  );
};

export default Navbar;
