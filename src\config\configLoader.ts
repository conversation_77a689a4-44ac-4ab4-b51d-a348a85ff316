import { WebsiteConfig } from './types';
import { defaultConfig } from './defaultConfig';
import configJson from './config.json';

// Function to load configuration from JSON file
export const loadConfigFromJson = (): WebsiteConfig => {
  try {
    // Type assertion to treat the imported JSON as WebsiteConfig
    return configJson as WebsiteConfig;
  } catch (error) {
    console.error('Error loading configuration from JSON:', error);
    return defaultConfig;
  }
};

// Function to merge default config with loaded config
export const mergeWithDefaultConfig = (loadedConfig: Partial<WebsiteConfig>): WebsiteConfig => {
  return {
    ...defaultConfig,
    ...loadedConfig,
  };
};

// Function to load configuration from localStorage
export const loadConfigFromLocalStorage = (): WebsiteConfig | null => {
  try {
    const savedConfig = localStorage.getItem('websiteConfig');
    if (savedConfig) {
      const parsedConfig = JSON.parse(savedConfig) as WebsiteConfig;
      console.log('Loaded config from localStorage:', parsedConfig);
      return parsedConfig;
    }
    return null;
  } catch (error) {
    console.error('Error loading configuration from localStorage:', error);
    return null;
  }
};

// Function to save configuration to localStorage
export const saveConfigToLocalStorage = (config: WebsiteConfig): void => {
  try {
    // Create a deep copy to avoid any reference issues
    const configCopy = JSON.parse(JSON.stringify(config));
    const configString = JSON.stringify(configCopy);
    localStorage.setItem('websiteConfig', configString);
    console.log('Saved config to localStorage, size:', configString.length);
    console.log('Config sample:', {
      siteName: configCopy.siteName,
      pages: {
        properties: configCopy.propertiesPage.enabled,
        projects: configCopy.projectsPage.enabled,
        about: configCopy.aboutPage.enabled,
        contact: configCopy.contactPage.enabled
      }
    });
  } catch (error) {
    console.error('Error saving configuration to localStorage:', error);
  }
};

// Function to get the active configuration
export const getActiveConfig = (): WebsiteConfig => {
  // First try to load from localStorage
  const localConfig = loadConfigFromLocalStorage();
  if (localConfig) {
    console.log('Using config from localStorage');
    console.log('Config sample from localStorage:', {
      siteName: localConfig.siteName,
      pages: {
        properties: localConfig.propertiesPage.enabled,
        projects: localConfig.projectsPage.enabled,
        about: localConfig.aboutPage.enabled,
        contact: localConfig.contactPage.enabled
      }
    });
    return localConfig;
  }

  // If not in localStorage, load from JSON file
  console.log('Using config from JSON file');
  const jsonConfig = loadConfigFromJson();
  return jsonConfig;
};
