import React from 'react';
import { CustomPageComponent } from '@/config/types';

interface HeroComponentProps {
  component: CustomPageComponent;
}

const HeroComponent: React.FC<HeroComponentProps> = ({ component }) => {
  const { title, subtitle, imageUrl, layout = 'center' } = component;

  console.log('HeroComponent rendering with:', { title, subtitle, imageUrl, layout });

  const backgroundStyle = imageUrl ? {
    backgroundImage: `linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.5)), url(${imageUrl})`,
    backgroundSize: 'cover',
    backgroundPosition: 'center',
  } : {};

  // Determine text alignment class
  let alignmentClass = 'text-center';
  if (layout === 'left') alignmentClass = 'text-left';
  if (layout === 'right') alignmentClass = 'text-right';

  return (
    <div
      className={`w-full py-20 ${imageUrl ? 'text-white' : 'bg-gray-50'}`}
      style={backgroundStyle}
    >
      <div className="container mx-auto px-4">
        <div className={`max-w-4xl mx-auto ${alignmentClass}`}>
          {title && <h1 className="text-4xl md:text-5xl font-bold mb-4">{title}</h1>}
          {subtitle && <p className="text-xl md:text-2xl">{subtitle}</p>}
        </div>
      </div>
    </div>
  );
};

export default HeroComponent;
