import React from 'react';
import { CustomPageComponent } from '@/config/types';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

interface TextComponentEditorProps {
  component: CustomPageComponent;
  onUpdate: (component: CustomPageComponent) => void;
}

const TextComponentEditor: React.FC<TextComponentEditorProps> = ({ component, onUpdate }) => {
  // Handle component property changes
  const handleChange = (property: string, value: any) => {
    onUpdate({
      ...component,
      [property]: value
    });
  };

  return (
    <div className="space-y-4">
      <div className="space-y-2">
        <Label htmlFor="text-title">Title</Label>
        <Input
          id="text-title"
          value={component.title || ''}
          onChange={(e) => handleChange('title', e.target.value)}
          placeholder="Section Title"
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="text-subtitle">Subtitle</Label>
        <Input
          id="text-subtitle"
          value={component.subtitle || ''}
          onChange={(e) => handleChange('subtitle', e.target.value)}
          placeholder="Section Subtitle"
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="text-content">Content (HTML)</Label>
        <Textarea
          id="text-content"
          value={component.content || ''}
          onChange={(e) => handleChange('content', e.target.value)}
          placeholder="<p>Your content here...</p>"
          rows={6}
          className="font-mono text-sm"
        />
        <p className="text-xs text-muted-foreground">
          You can use HTML tags for formatting (p, h1-h6, ul, ol, li, strong, em, etc.)
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="text-layout">Text Alignment</Label>
          <Select
            value={component.layout || 'left'}
            onValueChange={(value) => handleChange('layout', value)}
          >
            <SelectTrigger id="text-layout">
              <SelectValue placeholder="Select alignment" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="left">Left</SelectItem>
              <SelectItem value="center">Center</SelectItem>
              <SelectItem value="right">Right</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <Label htmlFor="text-bg-color">Background Color</Label>
          <div className="flex gap-2">
            <Input
              id="text-bg-color"
              type="color"
              value={component.backgroundColor || '#ffffff'}
              onChange={(e) => handleChange('backgroundColor', e.target.value)}
              className="w-12 p-1 h-10"
            />
            <Input
              value={component.backgroundColor || ''}
              onChange={(e) => handleChange('backgroundColor', e.target.value)}
              placeholder="#ffffff or transparent"
              className="flex-1"
            />
          </div>
        </div>
      </div>

      <div className="space-y-2">
        <Label htmlFor="text-color">Text Color</Label>
        <div className="flex gap-2">
          <Input
            id="text-color"
            type="color"
            value={component.textColor || '#000000'}
            onChange={(e) => handleChange('textColor', e.target.value)}
            className="w-12 p-1 h-10"
          />
          <Input
            value={component.textColor || ''}
            onChange={(e) => handleChange('textColor', e.target.value)}
            placeholder="#000000 or inherit"
            className="flex-1"
          />
        </div>
      </div>
    </div>
  );
};

export default TextComponentEditor;
