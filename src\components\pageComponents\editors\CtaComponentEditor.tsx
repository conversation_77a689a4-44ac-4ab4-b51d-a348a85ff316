import React from 'react';
import { CustomPageComponent } from '@/config/types';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';

interface CtaComponentEditorProps {
  component: CustomPageComponent;
  onUpdate: (component: CustomPageComponent) => void;
}

const CtaComponentEditor: React.FC<CtaComponentEditorProps> = ({ component, onUpdate }) => {
  // Handle component property changes
  const handleChange = (property: string, value: any) => {
    onUpdate({
      ...component,
      [property]: value
    });
  };

  return (
    <div className="space-y-4">
      <div className="space-y-2">
        <Label htmlFor="cta-title">Title</Label>
        <Input
          id="cta-title"
          value={component.title || ''}
          onChange={(e) => handleChange('title', e.target.value)}
          placeholder="Call to Action Title"
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="cta-content">Content</Label>
        <Textarea
          id="cta-content"
          value={component.content || ''}
          onChange={(e) => handleChange('content', e.target.value)}
          placeholder="Call to action message..."
          rows={3}
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="cta-button-text">Button Text</Label>
          <Input
            id="cta-button-text"
            value={component.buttonText || ''}
            onChange={(e) => handleChange('buttonText', e.target.value)}
            placeholder="Click Here"
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="cta-button-link">Button Link</Label>
          <Input
            id="cta-button-link"
            value={component.buttonLink || ''}
            onChange={(e) => handleChange('buttonLink', e.target.value)}
            placeholder="/website/contact"
          />
        </div>
      </div>

      <div className="space-y-2">
        <Label htmlFor="cta-bg-color">Background Color</Label>
        <div className="flex gap-2">
          <Input
            id="cta-bg-color"
            type="color"
            value={component.backgroundColor || '#f8fafc'}
            onChange={(e) => handleChange('backgroundColor', e.target.value)}
            className="w-12 p-1 h-10"
          />
          <Input
            value={component.backgroundColor || ''}
            onChange={(e) => handleChange('backgroundColor', e.target.value)}
            placeholder="#f8fafc"
            className="flex-1"
          />
        </div>
      </div>

      <div className="space-y-2">
        <Label htmlFor="cta-text-color">Text Color</Label>
        <div className="flex gap-2">
          <Input
            id="cta-text-color"
            type="color"
            value={component.textColor || '#000000'}
            onChange={(e) => handleChange('textColor', e.target.value)}
            className="w-12 p-1 h-10"
          />
          <Input
            value={component.textColor || ''}
            onChange={(e) => handleChange('textColor', e.target.value)}
            placeholder="#000000 or inherit"
            className="flex-1"
          />
        </div>
      </div>
    </div>
  );
};

export default CtaComponentEditor;
