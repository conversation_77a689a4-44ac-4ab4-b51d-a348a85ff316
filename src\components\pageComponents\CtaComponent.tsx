import React from 'react';
import { CustomPageComponent } from '@/config/types';
import { Button } from '@/components/ui/button';
import { Link } from 'react-router-dom';

interface CtaComponentProps {
  component: CustomPageComponent;
}

const CtaComponent: React.FC<CtaComponentProps> = ({ component }) => {
  const {
    title,
    content,
    buttonText,
    buttonLink,
    backgroundColor = '#f8fafc',
    textColor
  } = component;

  console.log('CtaComponent rendering with:', { title, content, buttonText, buttonLink });

  const containerStyle = {
    backgroundColor,
    color: textColor || 'inherit',
  };

  return (
    <div
      className="py-16"
      style={containerStyle}
    >
      <div className="container mx-auto px-4">
        <div className="max-w-3xl mx-auto text-center">
          {title && <h2 className="text-3xl font-bold mb-4">{title}</h2>}
          {content && <p className="text-xl mb-8">{content}</p>}
          {buttonText && buttonLink && (
            <Button asChild size="lg">
              <Link to={buttonLink}>{buttonText}</Link>
            </Button>
          )}
        </div>
      </div>
    </div>
  );
};

export default CtaComponent;
