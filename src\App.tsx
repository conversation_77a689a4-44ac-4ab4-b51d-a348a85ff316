
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route, Navigate } from "react-router-dom";
import ScrollToTop from "./components/ScrollToTop";
import DocumentTitle from "./components/DocumentTitle";
import Index from "./pages/Index";
import Properties from "./pages/Properties";
import PropertyDetail from "./pages/PropertyDetail";
import Projects from "./pages/Projects";
import ProjectDetail from "./pages/ProjectDetail";
import About from "./pages/About";
import Contact from "./pages/Contact";
import NotFound from "./pages/NotFound";
import Login from "./pages/Login";
import CustomPage from "./pages/CustomPage";
import { useConfig } from "./config/ConfigContext";
import { AuthProvider } from "./auth/AuthContext";
import ProtectedRoute from "./auth/ProtectedRoute";
import AdminPanel from "./components/AdminPanel";

const queryClient = new QueryClient();

const App = () => {
  const { config } = useConfig();

  // Log the current configuration for debugging
  console.log('Current config in App.tsx:', config);
  console.log('Pages enabled status:', {
    properties: config.propertiesPage.enabled,
    projects: config.projectsPage.enabled,
    about: config.aboutPage.enabled,
    contact: config.contactPage.enabled
  });

  return (
  <QueryClientProvider client={queryClient}>
    <TooltipProvider>
      <Toaster />
      <Sonner />
      <AuthProvider>
        <BrowserRouter>
          <ScrollToTop />
          <DocumentTitle />
          <Routes>
            <Route path="/" element={<Navigate to="/website" replace />} />
            <Route path="/website" element={<Index />} />

            {config.propertiesPage.enabled && (
              <>
                <Route path="/website/properties" element={<Properties />} />
                <Route path="/website/properties/:id" element={<PropertyDetail />} />
              </>
            )}

            {config.projectsPage.enabled && (
              <>
                <Route path="/website/projects" element={<Projects />} />
                <Route path="/website/projects/:id" element={<ProjectDetail />} />
              </>
            )}

            {config.aboutPage.enabled && (
              <Route path="/website/about" element={<About />} />
            )}

            {config.contactPage.enabled && (
              <Route path="/website/contact" element={<Contact />} />
            )}

            {/* Custom pages */}
            <Route path="/website/page/:slug" element={<CustomPage />} />

            {/* Login page */}
            <Route path="/website/login" element={<Login />} />

            {/* Protected admin route */}
            <Route path="/website/admin" element={
              <ProtectedRoute>
                <AdminPanel />
              </ProtectedRoute>
            } />

            <Route path="*" element={<NotFound />} />
          </Routes>
        </BrowserRouter>
      </AuthProvider>
    </TooltipProvider>
  </QueryClientProvider>
  );
};

export default App;
