import React from 'react';
import { useConfig } from '@/config/ConfigContext';
import DynamicForm from './DynamicForm';
import { Card, CardContent } from '@/components/ui/card';
import { AlertCircle } from 'lucide-react';

interface ConfigurableFormProps {
  formId: string;
}

const ConfigurableForm: React.FC<ConfigurableFormProps> = ({ formId }) => {
  const { config } = useConfig();
  
  // Check if forms exist in the configuration
  if (!config.forms) {
    return (
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center text-red-500">
            <AlertCircle className="h-4 w-4 mr-2" />
            <p>No forms defined in the configuration</p>
          </div>
        </CardContent>
      </Card>
    );
  }
  
  // Get the form configuration
  const formConfig = config.forms[formId];
  
  if (!formConfig) {
    return (
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center text-yellow-500">
            <AlertCircle className="h-4 w-4 mr-2" />
            <p>Form with ID "{formId}" not found</p>
          </div>
        </CardContent>
      </Card>
    );
  }
  
  // Check if the form is enabled
  if (!formConfig.enabled) {
    return null;
  }
  
  return (
    <DynamicForm
      id={formConfig.id}
      title={formConfig.title}
      description={formConfig.description}
      submitButtonText={formConfig.submitButtonText}
      successMessage={formConfig.successMessage}
      fields={formConfig.fields || []}
    />
  );
};

export default ConfigurableForm;
