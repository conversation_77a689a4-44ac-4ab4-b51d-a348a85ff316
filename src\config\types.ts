export interface ApiConfig {
  enabled: boolean;
  url: string;
  method: 'GET' | 'POST';
  headers?: Record<string, string>;
  bodyParams?: Record<string, any>;
  responseMapping: {
    items: string; // JSON path to the array of items (e.g., "data.results")
    id: string;
    title: string;
    description?: string;
    image?: string;
    price?: string;
    location?: string;
    features?: string;
    url?: string;
    [key: string]: string | undefined;
  };
  cacheDuration?: number; // Cache duration in minutes
}

export interface SectionConfig {
  enabled: boolean;
  title: string;
  subtitle?: string;
}

export interface HeroConfig {
  enabled: boolean;
  title: string;
  subtitle: string;
  backgroundImage: string;
  buttonText: string;
  buttonLink: string;
}

export interface NavLinkConfig {
  name: string;
  path: string;
  enabled: boolean;
}

export interface ContactInfoConfig {
  address: string[];
  phone: string;
  email: string;
}

export interface SocialLinkConfig {
  name: string;
  url: string;
  enabled: boolean;
}

export interface FeatureConfig {
  title: string;
  description: string;
  icon: string;
  enabled: boolean;
}

export interface FormFieldConfig {
  id: string;
  label: string;
  type: string;
  placeholder?: string;
  required?: boolean;
  options?: Array<{ label: string; value: string }>;
  width?: 'full' | 'half';
  rows?: number;
  order?: number;
}

export interface FormConfig {
  id: string;
  enabled: boolean;
  title: string;
  description?: string;
  submitButtonText: string;
  successMessage?: string;
  layout?: 'vertical' | 'horizontal';
  location?: string;
  displayOrder?: number;
  apiEndpoint?: string;
  apiMethod?: 'POST' | 'PUT';
  apiHeaders?: Record<string, string>;
  redirectAfterSubmit?: string;
  fields: FormFieldConfig[];
}

export interface CustomPageComponent {
  type: string;
  id: string;
  title?: string;
  subtitle?: string;
  content?: string;
  imageUrl?: string;
  buttonText?: string;
  buttonLink?: string;
  backgroundColor?: string;
  textColor?: string;
  layout?: 'left' | 'right' | 'center' | 'full';
  columns?: number;
  items?: any[];
  apiEndpoint?: string;
  [key: string]: any;
}

export interface CustomPageConfig {
  id: string;
  title: string;
  slug: string;
  description?: string;
  enabled: boolean;
  showInNavigation: boolean;
  navigationOrder?: number;
  components: CustomPageComponent[];
  seo?: {
    title?: string;
    description?: string;
    keywords?: string;
    ogImage?: string;
  };
}

export interface WebsiteConfig {
  // General site settings
  siteName: string;
  logoText: string;
  headerLogoImage?: string;
  footerLogoImage?: string;
  theme?: 'light' | 'dark' | 'blue' | 'green' | 'purple' | 'orange';

  // Navigation
  navigation: {
    links: NavLinkConfig[];
    ctaButton: {
      text: string;
      link: string;
      enabled: boolean;
    };
  };

  // Home page
  homePage: {
    hero: HeroConfig;
    featuredProperties: SectionConfig;
    featuredProjects: SectionConfig;
    whyChooseUs: {
      enabled: boolean;
      title: string;
      subtitle: string;
      features: FeatureConfig[];
    };
    testimonials: SectionConfig;
    callToAction: {
      enabled: boolean;
      title: string;
      text: string;
      primaryButton: {
        text: string;
        link: string;
      };
      secondaryButton: {
        text: string;
        link: string;
        enabled: boolean;
      };
    };
    heroForm?: {
      enabled: boolean;
      formId: string;
    };
  };

  // About page
  aboutPage: {
    enabled: boolean;
    pageHeader: {
      title: string;
      subtitle: string;
      backgroundImage: string;
    };
    ourStory: SectionConfig;
    missionValues: SectionConfig;
    team: SectionConfig;
    callToAction: {
      enabled: boolean;
      title: string;
      text: string;
      buttonText: string;
      buttonLink: string;
    };
  };

  // Properties page
  propertiesPage: {
    enabled: boolean;
    pageHeader: {
      title: string;
      subtitle: string;
      backgroundImage: string;
    };
    searchSection: SectionConfig;
    api?: ApiConfig;
  };

  // Projects page
  projectsPage: {
    enabled: boolean;
    pageHeader: {
      title: string;
      subtitle: string;
      backgroundImage: string;
    };
    searchSection: SectionConfig;
    api?: ApiConfig;
  };

  // Contact page
  contactPage: {
    enabled: boolean;
    pageHeader: {
      title: string;
      subtitle: string;
      backgroundImage: string;
    };
    contactForm: {
      enabled: boolean;
      title: string;
      submitButtonText: string;
    };
    contactInfo: ContactInfoConfig;
    officesSection: {
      enabled: boolean;
      title: string;
    };
    mapSection: {
      enabled: boolean;
      title: string;
    };
  };

  // Footer
  footer: {
    companyInfo: {
      description: string;
    };
    socialLinks: SocialLinkConfig[];
    quickLinks: {
      title: string;
      links: NavLinkConfig[];
    };
    contactInfo: ContactInfoConfig;
    copyright: string;
  };

  // Forms configuration
  forms?: Record<string, FormConfig>;

  // Custom pages
  customPages?: Record<string, CustomPageConfig>;
}
