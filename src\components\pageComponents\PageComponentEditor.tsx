import React, { useState } from 'react';
import { CustomPageComponent } from '@/config/types';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { 
  Trash2, 
  MoveUp, 
  MoveDown, 
  ChevronDown,
  ChevronUp,
  Edit,
  Image,
  Layout,
  Type,
  Link,
  PaintBucket,
  Plus,
  Minus
} from 'lucide-react';
import HeroComponentEditor from './editors/HeroComponentEditor';
import TextComponentEditor from './editors/TextComponentEditor';
import FeaturesComponentEditor from './editors/FeaturesComponentEditor';
import CtaComponentEditor from './editors/CtaComponentEditor';
import AccordionComponentEditor from './editors/AccordionComponentEditor';

interface PageComponentEditorProps {
  component: CustomPageComponent;
  index: number;
  isFirst: boolean;
  isLast: boolean;
  onUpdate: (component: CustomPageComponent) => void;
  onRemove: () => void;
  onMoveUp: () => void;
  onMoveDown: () => void;
}

const PageComponentEditor: React.FC<PageComponentEditorProps> = ({
  component,
  index,
  isFirst,
  isLast,
  onUpdate,
  onRemove,
  onMoveUp,
  onMoveDown
}) => {
  const [isOpen, setIsOpen] = useState(false);

  // Get component type display name
  const getComponentTypeName = (type: string) => {
    switch (type) {
      case 'hero': return 'Hero Section';
      case 'text': return 'Text Section';
      case 'features': return 'Features Grid';
      case 'cta': return 'Call to Action';
      case 'accordion': return 'Accordion';
      default: return type.charAt(0).toUpperCase() + type.slice(1);
    }
  };

  // Get component icon
  const getComponentIcon = (type: string) => {
    switch (type) {
      case 'hero': return <Image className="h-4 w-4" />;
      case 'text': return <Type className="h-4 w-4" />;
      case 'features': return <Layout className="h-4 w-4" />;
      case 'cta': return <Link className="h-4 w-4" />;
      case 'accordion': return <ChevronDown className="h-4 w-4" />;
      default: return <Edit className="h-4 w-4" />;
    }
  };

  // Render the appropriate editor based on component type
  const renderComponentEditor = () => {
    switch (component.type) {
      case 'hero':
        return <HeroComponentEditor component={component} onUpdate={onUpdate} />;
      case 'text':
        return <TextComponentEditor component={component} onUpdate={onUpdate} />;
      case 'features':
        return <FeaturesComponentEditor component={component} onUpdate={onUpdate} />;
      case 'cta':
        return <CtaComponentEditor component={component} onUpdate={onUpdate} />;
      case 'accordion':
        return <AccordionComponentEditor component={component} onUpdate={onUpdate} />;
      default:
        return (
          <div className="p-4 bg-gray-100 rounded-md">
            <p className="text-muted-foreground">No editor available for component type: {component.type}</p>
          </div>
        );
    }
  };

  return (
    <Card className="border-l-4" style={{ borderLeftColor: isOpen ? 'var(--primary)' : 'var(--border)' }}>
      <Collapsible open={isOpen} onOpenChange={setIsOpen}>
        <div className="flex items-center justify-between p-4">
          <div className="flex items-center gap-2">
            <div className="bg-muted p-1.5 rounded">
              {getComponentIcon(component.type)}
            </div>
            <div>
              <h3 className="text-sm font-medium">{component.title || getComponentTypeName(component.type)}</h3>
              <p className="text-xs text-muted-foreground">{getComponentTypeName(component.type)}</p>
            </div>
          </div>
          <div className="flex items-center gap-1">
            <Button
              variant="ghost"
              size="icon"
              onClick={onMoveUp}
              disabled={isFirst}
              title="Move Up"
            >
              <MoveUp className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="icon"
              onClick={onMoveDown}
              disabled={isLast}
              title="Move Down"
            >
              <MoveDown className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="icon"
              onClick={onRemove}
              title="Remove"
            >
              <Trash2 className="h-4 w-4" />
            </Button>
            <CollapsibleTrigger asChild>
              <Button variant="ghost" size="icon">
                {isOpen ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
              </Button>
            </CollapsibleTrigger>
          </div>
        </div>
        <CollapsibleContent>
          <CardContent className="pt-0 pb-4">
            {renderComponentEditor()}
          </CardContent>
        </CollapsibleContent>
      </Collapsible>
    </Card>
  );
};

export default PageComponentEditor;
