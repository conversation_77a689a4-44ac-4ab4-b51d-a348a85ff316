import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';

interface ThemePreviewProps {
  theme: string;
}

const ThemePreview: React.FC<ThemePreviewProps> = ({ theme }) => {
  return (
    <div data-theme={theme} className="p-4 rounded-lg border">
      <h3 className="text-lg font-medium mb-4">Theme Preview: {theme.charAt(0).toUpperCase() + theme.slice(1)}</h3>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <Card>
          <CardHeader>
            <CardTitle>Card Example</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-foreground mb-4">This is how content will look with the selected theme.</p>
            <div className="flex flex-wrap gap-2">
              <Button variant="default">Primary</Button>
              <Button variant="secondary">Secondary</Button>
              <Button variant="outline">Outline</Button>
              <Button variant="ghost">Ghost</Button>
            </div>
          </CardContent>
        </Card>
        
        <div className="space-y-4">
          <div className="p-4 bg-background border rounded-lg">
            <h4 className="font-medium mb-2">Background</h4>
            <p className="text-foreground text-sm">Text on background</p>
          </div>
          
          <div className="p-4 bg-primary text-primary-foreground rounded-lg">
            <h4 className="font-medium mb-2">Primary</h4>
            <p className="text-sm">Text on primary</p>
          </div>
          
          <div className="p-4 bg-secondary text-secondary-foreground rounded-lg">
            <h4 className="font-medium mb-2">Secondary</h4>
            <p className="text-sm">Text on secondary</p>
          </div>
          
          <div className="p-4 bg-accent text-accent-foreground rounded-lg">
            <h4 className="font-medium mb-2">Accent</h4>
            <p className="text-sm">Text on accent</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ThemePreview;
