{"siteName": "API Loaded Configuration", "logoText": "API Demo", "forms": {"contactForm": {"id": "contactForm", "enabled": true, "title": "Contact Us via API", "description": "This form was loaded from an API endpoint.", "submitButtonText": "Send Message", "successMessage": "Thank you for your message. We will get back to you shortly.", "layout": "vertical", "location": "contact", "displayOrder": 1, "fields": [{"id": "name", "label": "Full Name", "type": "text", "placeholder": "Your name", "required": true, "width": "half", "order": 1}, {"id": "email", "label": "Email", "type": "email", "placeholder": "<EMAIL>", "required": true, "width": "half", "order": 2}, {"id": "phone", "label": "Phone", "type": "tel", "placeholder": "Your phone number", "required": false, "width": "half", "order": 3}, {"id": "message", "label": "Message", "type": "textarea", "placeholder": "Your message", "required": true, "rows": 4, "width": "full", "order": 4}]}, "form_1744140094766": {"id": "form_1744140094766", "enabled": true, "title": "Home Page Form from API", "description": "This form was loaded from an API endpoint and is configured to display on the home page.", "submitButtonText": "Submit Form", "successMessage": "Thank you for your submission!", "layout": "vertical", "location": "home", "displayOrder": 1, "fields": [{"id": "name", "label": "Your Name", "type": "text", "placeholder": "Enter your name", "required": true, "width": "full", "order": 1}, {"id": "email", "label": "Email Address", "type": "email", "placeholder": "<EMAIL>", "required": true, "width": "full", "order": 2}, {"id": "interest", "label": "What are you interested in?", "type": "select", "required": true, "options": [{"label": "Properties", "value": "properties"}, {"label": "Projects", "value": "projects"}, {"label": "Investment", "value": "investment"}, {"label": "Other", "value": "other"}], "width": "full", "order": 3}, {"id": "message", "label": "Message", "type": "textarea", "placeholder": "Tell us more about your inquiry", "required": true, "rows": 4, "width": "full", "order": 4}]}}, "navigation": {"links": [{"name": "Home", "path": "/website", "enabled": true}, {"name": "Properties", "path": "/website/properties", "enabled": true}, {"name": "Projects", "path": "/website/projects", "enabled": true}, {"name": "About", "path": "/website/about", "enabled": true}, {"name": "Contact", "path": "/website/contact", "enabled": true}], "ctaButton": {"text": "Contact Us", "link": "/website/contact", "enabled": true}}, "homePage": {"hero": {"enabled": true, "title": "Find Your Dream Property", "subtitle": "Discover exceptional properties in prime locations", "backgroundImage": "https://images.unsplash.com/photo-1560518883-ce09059eeffa?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=2340&q=80", "buttonText": "View Properties", "buttonLink": "/website/properties"}, "featuredProperties": {"enabled": true, "title": "Featured Properties", "subtitle": "Explore our handpicked selection of premium properties"}, "featuredProjects": {"enabled": true, "title": "Featured Projects", "subtitle": "Discover our latest development projects"}, "whyChooseUs": {"enabled": true, "title": "Why Choose Us", "subtitle": "We provide exceptional real estate services", "features": [{"title": "Premium Locations", "description": "We offer properties in the most desirable and strategic locations.", "icon": "map", "enabled": true}, {"title": "Quality Assurance", "description": "All our properties meet the highest standards of quality and design.", "icon": "shield", "enabled": true}, {"title": "Expert Guidance", "description": "Our team of experts provides personalized guidance throughout your real estate journey.", "icon": "users", "enabled": true}]}, "testimonials": {"enabled": true, "title": "What Our Clients Say", "subtitle": "Hear from our satisfied clients about their experience working with us"}, "callToAction": {"enabled": true, "title": "Ready to Find Your Dream Property?", "text": "Contact us today to discuss your real estate needs or to schedule a viewing of any of our premium properties.", "primaryButton": {"text": "Browse Properties", "link": "/website/properties"}, "secondaryButton": {"text": "Contact Us", "link": "/website/contact", "enabled": true}}, "heroForm": {"enabled": true, "formId": "form_1744140094766"}}, "aboutPage": {"enabled": true, "pageHeader": {"title": "About Us", "subtitle": "Learn about our company and our mission to create exceptional real estate experiences", "backgroundImage": "https://images.unsplash.com/photo-1497366216548-37526070297c?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=2301&q=80"}, "ourStory": {"enabled": true, "title": "Our Story", "subtitle": "Learn about our journey and what makes us different"}, "missionValues": {"enabled": true, "title": "Our Mission & Values", "subtitle": "The principles that guide our business and relationships"}, "team": {"enabled": true, "title": "Our Team", "subtitle": "Meet the professionals behind our success"}, "callToAction": {"enabled": true, "title": "Work With Us", "text": "Whether you're looking for your dream home, a property investment, or a development project, our team is ready to help you achieve your real estate goals.", "buttonText": "Contact Our Team", "buttonLink": "/website/contact"}, "contactForm": {"enabled": false, "formId": "contactForm"}}, "contactPage": {"enabled": true, "pageHeader": {"title": "Contact Us", "subtitle": "Get in touch with our team for any inquiries or to schedule a viewing", "backgroundImage": "https://images.unsplash.com/photo-1542744173-8659b8e77b1a?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=2340&q=80"}, "contactForm": {"enabled": true, "title": "Send Us a Message", "submitButtonText": "Send Message", "formId": "contactForm"}, "contactInfo": {"address": ["123 Real Estate Avenue", "Cityville, State 12345", "United States"], "phone": "+****************", "email": "<EMAIL>"}}, "propertiesPage": {"enabled": true, "pageHeader": {"title": "Our Properties", "subtitle": "Discover our exclusive collection of premium properties available for sale and rent", "backgroundImage": "https://images.unsplash.com/photo-1560518883-ce09059eeffa?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=2340&q=80"}, "searchSection": {"enabled": true, "title": "Find Your Perfect Property", "subtitle": "Use our search tools to find properties that match your criteria"}, "inquiryForm": {"enabled": true, "formId": "propertyInquiryForm"}}, "projectsPage": {"enabled": true, "pageHeader": {"title": "Our Projects", "subtitle": "Explore our collection of premium development projects", "backgroundImage": "https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=2340&q=80"}, "searchSection": {"enabled": true, "title": "Discover Our Real Estate Projects", "subtitle": "Browse our portfolio of development projects across various locations"}, "inquiryForm": {"enabled": true, "formId": "projectInquiryForm"}}, "footer": {"contactInfo": {"address": ["123 Real Estate Avenue", "Cityville, State 12345", "United States"], "phone": "+****************", "email": "<EMAIL>"}, "socialLinks": {"facebook": "https://facebook.com", "twitter": "https://twitter.com", "instagram": "https://instagram.com", "linkedin": "https://linkedin.com"}, "copyright": "© 2023 Real Estate. All rights reserved."}}