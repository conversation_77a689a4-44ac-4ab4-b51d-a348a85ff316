
import Layout from '@/components/Layout';
import PageHeader from '@/components/PageHeader';
import Section from '@/components/Section';
import { Button } from '@/components/ui/button';
import { Link } from 'react-router-dom';
import { useConfig } from '@/config/ConfigContext';

const About = () => {
  const { config } = useConfig();
  const teamMembers = [
    {
      name: '<PERSON>',
      position: 'CEO & Founder',
      bio: 'With over 20 years of experience in real estate development and investment.',
      imageUrl: 'https://images.unsplash.com/photo-1560250097-0b93528c311a?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=2340&q=80',
    },
    {
      name: '<PERSON>',
      position: 'Chief Architect',
      bio: 'Award-winning architect specializing in sustainable luxury design.',
      imageUrl: 'https://images.unsplash.com/photo-1573496359142-b8d87734a5a2?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=2338&q=80',
    },
    {
      name: 'Michael Williams',
      position: 'Development Director',
      bio: 'Leads our project development teams with expertise in large-scale developments.',
      imageUrl: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=2340&q=80',
    },
    {
      name: 'Sarah Thompson',
      position: 'Sales Director',
      bio: 'Expert in luxury property marketing and client relationships.',
      imageUrl: 'https://images.unsplash.com/photo-1580489944761-15a19d654956?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1361&q=80',
    },
  ];

  return (
    <Layout>
      <PageHeader
        title={config.aboutPage.pageHeader.title}
        subtitle={config.aboutPage.pageHeader.subtitle}
        backgroundImage={config.aboutPage.pageHeader.backgroundImage}
      />

      {config.aboutPage.ourStory.enabled && (
        <Section
          title={config.aboutPage.ourStory.title}
          subtitle={config.aboutPage.ourStory.subtitle}
        >
        <div className="grid grid-cols-1 md:grid-cols-2 gap-12 items-center">
          <div>
            <h3 className="text-2xl font-semibold mb-4">Creating Exceptional Properties Since 2005</h3>
            <p className="text-gray-600 mb-4">
              Founded in 2005, RealEstate has established itself as a leader in the development and marketing of premium real estate properties. Our journey began with a vision to create exceptional living spaces that combine innovative design, quality craftsmanship, and sustainable practices.
            </p>
            <p className="text-gray-600 mb-4">
              Over the years, we have successfully delivered numerous high-end residential developments, luxury homes, and commercial properties across the country. Our commitment to excellence has earned us a reputation for creating properties that not only meet but exceed our clients' expectations.
            </p>
            <p className="text-gray-600">
              Today, RealEstate continues to grow and evolve, guided by our founding principles of integrity, quality, and client satisfaction. We remain dedicated to pushing the boundaries of design and development to create spaces that inspire and elevate the way people live, work, and play.
            </p>
          </div>
          <div className="rounded-lg overflow-hidden shadow-lg">
            <img
              src="https://images.unsplash.com/photo-1497366811353-6870744d04b2?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=2301&q=80"
              alt="Company Building"
              className="w-full h-auto"
            />
          </div>
        </div>
        </Section>
      )}

      {config.aboutPage.missionValues.enabled && (
        <Section title={config.aboutPage.missionValues.title} className="bg-gray-50">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          <div className="bg-white p-6 rounded-lg shadow-sm">
            <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mb-4">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                strokeWidth={1.5}
                stroke="currentColor"
                className="w-6 h-6 text-primary"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  d="M3.75 13.5l10.5-11.25L12 10.5h8.25L9.75 21.75 12 13.5H3.75z"
                />
              </svg>
            </div>
            <h3 className="text-xl font-semibold mb-2">Excellence</h3>
            <p className="text-gray-600">
              We are committed to delivering excellence in every aspect of our work, from design and construction to customer service and after-sales support.
            </p>
          </div>

          <div className="bg-white p-6 rounded-lg shadow-sm">
            <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mb-4">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                strokeWidth={1.5}
                stroke="currentColor"
                className="w-6 h-6 text-primary"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  d="M15.75 5.25a3 3 0 013 3m3 0a6 6 0 01-7.029 5.912c-.563-.097-1.159.026-1.563.43L10.5 17.25H8.25v2.25H6v2.25H2.25v-2.818c0-.597.237-1.17.659-1.591l6.499-6.499c.404-.404.527-1 .43-1.563A6 6 0 1121.75 8.25z"
                />
              </svg>
            </div>
            <h3 className="text-xl font-semibold mb-2">Integrity</h3>
            <p className="text-gray-600">
              We conduct our business with honesty, transparency, and respect, building trust with our clients, partners, and communities.
            </p>
          </div>

          <div className="bg-white p-6 rounded-lg shadow-sm">
            <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mb-4">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                strokeWidth={1.5}
                stroke="currentColor"
                className="w-6 h-6 text-primary"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  d="M15.59 14.37a6 6 0 01-5.84 7.38v-4.8m5.84-2.58a14.98 14.98 0 006.16-12.12A14.98 14.98 0 009.631 8.41m5.96 5.96a14.926 14.926 0 01-5.841 2.58m-.119-8.54a6 6 0 00-7.381 5.84h4.8m2.581-5.84a14.927 14.927 0 00-2.58 5.84m2.699 2.7c-.103.021-.207.041-.311.06a15.09 15.09 0 01-2.448-2.448 14.9 14.9 0 01.06-.312m-2.24 2.39a4.493 4.493 0 00-1.757 4.306 4.493 4.493 0 004.306-1.758M16.5 9a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0z"
                />
              </svg>
            </div>
            <h3 className="text-xl font-semibold mb-2">Innovation</h3>
            <p className="text-gray-600">
              We embrace innovation and creativity, constantly seeking new ways to improve our designs, processes, and services to better serve our clients.
            </p>
          </div>
        </div>
        </Section>
      )}

      {config.aboutPage.team.enabled && (
        <Section
          title={config.aboutPage.team.title}
          subtitle={config.aboutPage.team.subtitle}
        >
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {teamMembers.map((member, index) => (
            <div key={index} className="text-center">
              <div className="w-36 h-36 rounded-full overflow-hidden mx-auto mb-4">
                <img
                  src={member.imageUrl}
                  alt={member.name}
                  className="w-full h-full object-cover"
                />
              </div>
              <h3 className="text-xl font-semibold">{member.name}</h3>
              <p className="text-primary mb-2">{member.position}</p>
              <p className="text-gray-600 text-sm">{member.bio}</p>
            </div>
          ))}
        </div>
        </Section>
      )}

      {config.aboutPage.callToAction.enabled && (
        <Section
          title={config.aboutPage.callToAction.title}
          className="bg-primary text-white"
        >
          <div className="text-center max-w-2xl mx-auto">
            <p className="text-lg mb-8 text-white/90">
              {config.aboutPage.callToAction.text}
            </p>
            <Button asChild size="lg" variant="outline" className="bg-transparent text-white hover:bg-white/10 border-white">
              <Link to={config.aboutPage.callToAction.buttonLink}>{config.aboutPage.callToAction.buttonText}</Link>
            </Button>
          </div>
        </Section>
      )}
    </Layout>
  );
};

export default About;
