import { WebsiteConfig } from './types';

export const defaultConfig: WebsiteConfig = {
  siteName: "RealEstate",
  logoText: "RealEstate",
  headerLogoImage: "",
  footerLogoImage: "",
  theme: "light",

  navigation: {
    links: [
      { name: 'Home', path: '/website', enabled: true },
      { name: 'Projects', path: '/website/projects', enabled: true },
      { name: 'Properties', path: '/website/properties', enabled: true },
      { name: 'About', path: '/website/about', enabled: true },
      { name: 'Contact', path: '/website/contact', enabled: true },
    ],
    ctaButton: {
      text: "Get In Touch",
      link: "/website/contact",
      enabled: true
    }
  },

  homePage: {
    hero: {
      enabled: true,
      title: "Discover Your Dream Property",
      subtitle: "We provide exceptional real estate services with a focus on luxury properties and developments.",
      backgroundImage: "https://images.unsplash.com/photo-1582407947304-fd86f028f716?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=2340&q=80",
      buttonText: "Explore Properties",
      buttonLink: "/website/properties"
    },
    featuredProperties: {
      enabled: true,
      title: "Featured Properties",
      subtitle: "Explore our handpicked selection of premium properties"
    },
    featuredProjects: {
      enabled: true,
      title: "Our Projects",
      subtitle: "Discover our latest development projects"
    },
    whyChooseUs: {
      enabled: true,
      title: "Why Choose Us",
      subtitle: "We deliver exceptional real estate services with a focus on quality and client satisfaction",
      features: [
        {
          title: "Premium Locations",
          description: "We offer properties in the most desirable and prestigious locations.",
          icon: "home",
          enabled: true
        },
        {
          title: "Quality Construction",
          description: "Our properties are built with the highest standards of construction and materials.",
          icon: "building",
          enabled: true
        },
        {
          title: "Expert Guidance",
          description: "Our team of experts provides personalized guidance throughout your real estate journey.",
          icon: "users",
          enabled: true
        }
      ]
    },
    testimonials: {
      enabled: true,
      title: "What Our Clients Say",
      subtitle: "Hear from our satisfied clients about their experience working with us"
    },
    callToAction: {
      enabled: true,
      title: "Ready to Find Your Dream Property?",
      text: "Contact us today to discuss your real estate needs or to schedule a viewing of any of our premium properties.",
      primaryButton: {
        text: "Browse Properties",
        link: "/website/properties"
      },
      secondaryButton: {
        text: "Contact Us",
        link: "/website/contact",
        enabled: true
      }
    },
    heroForm: {
      enabled: false,
      formId: ""
    }
  },

  aboutPage: {
    enabled: true,
    pageHeader: {
      title: "About Us",
      subtitle: "Learn about our company and our mission to create exceptional real estate experiences",
      backgroundImage: "https://images.unsplash.com/photo-1497366216548-37526070297c?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=2301&q=80"
    },
    ourStory: {
      enabled: true,
      title: "Our Story",
      subtitle: "Learn about our journey and what makes us different"
    },
    missionValues: {
      enabled: true,
      title: "Our Mission & Values",
      subtitle: "The principles that guide our business and relationships"
    },
    team: {
      enabled: true,
      title: "Our Team",
      subtitle: "Meet the professionals behind our success"
    },
    callToAction: {
      enabled: true,
      title: "Work With Us",
      text: "Whether you're looking for your dream home, a property investment, or a development project, our team is ready to help you achieve your real estate goals.",
      buttonText: "Contact Our Team",
      buttonLink: "/website/contact"
    }
  },

  propertiesPage: {
    enabled: true,
    pageHeader: {
      title: "Our Properties",
      subtitle: "Discover our exclusive collection of premium properties available for sale and rent",
      backgroundImage: "https://images.unsplash.com/photo-1560518883-ce09059eeffa?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=2340&q=80"
    },
    searchSection: {
      enabled: true,
      title: "Find Your Perfect Property",
      subtitle: "Use our search tools to find properties that match your criteria"
    },
    api: {
      enabled: false,
      url: "",
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        "Accept": "application/json"
      },
      responseMapping: {
        items: "data",
        id: "id",
        title: "name",
        description: "description",
        image: "image_url",
        price: "price",
        location: "location",
        features: "features",
        url: "url"
      },
      cacheDuration: 30
    }
  },

  projectsPage: {
    enabled: true,
    pageHeader: {
      title: "Our Projects",
      subtitle: "Explore our collection of premium development projects",
      backgroundImage: "https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=2340&q=80"
    },
    searchSection: {
      enabled: true,
      title: "Discover Our Real Estate Projects",
      subtitle: "Browse our portfolio of development projects across various locations"
    },
    api: {
      enabled: false,
      url: "",
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        "Accept": "application/json"
      },
      responseMapping: {
        items: "data",
        id: "id",
        title: "name",
        description: "description",
        image: "image_url",
        status: "status",
        location: "location",
        features: "features",
        url: "url"
      },
      cacheDuration: 30
    }
  },

  contactPage: {
    enabled: true,
    pageHeader: {
      title: "Contact Us",
      subtitle: "Get in touch with our team for any inquiries or to schedule a viewing",
      backgroundImage: "https://images.unsplash.com/photo-1542744173-8659b8e77b1a?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=2340&q=80"
    },
    contactForm: {
      enabled: true,
      title: "Send Us a Message",
      submitButtonText: "Send Message"
    },
    contactInfo: {
      address: ["123 Real Estate Avenue", "Property City, PC 12345"],
      phone: "(*************",
      email: "<EMAIL>"
    },
    officesSection: {
      enabled: true,
      title: "Our Offices"
    },
    mapSection: {
      enabled: true,
      title: "Location"
    }
  },

  footer: {
    companyInfo: {
      description: "Premium real estate solutions delivering exceptional properties and projects to meet your investment and lifestyle needs."
    },
    socialLinks: [
      { name: "Facebook", url: "#", enabled: true },
      { name: "Instagram", url: "#", enabled: true },
      { name: "LinkedIn", url: "#", enabled: true }
    ],
    quickLinks: {
      title: "Quick Links",
      links: [
        { name: "Home", path: "/website", enabled: true },
        { name: "Properties", path: "/website/properties", enabled: true },
        { name: "Projects", path: "/website/projects", enabled: true },
        { name: "About", path: "/website/about", enabled: true },
        { name: "Contact", path: "/website/contact", enabled: true }
      ]
    },
    contactInfo: {
      address: ["123 Real Estate Avenue", "Property City, PC 12345"],
      phone: "(*************",
      email: "<EMAIL>"
    },
    copyright: "© 2023 RealEstate. All rights reserved."
  },

  // Custom pages
  customPages: {
    services: {
      id: "services",
      title: "Our Services",
      slug: "services",
      description: "Explore our comprehensive real estate services",
      enabled: true,
      showInNavigation: true,
      navigationOrder: 3,
      components: [
        {
          type: "hero",
          id: "services-hero",
          title: "Our Services",
          subtitle: "Comprehensive real estate solutions tailored to your needs",
          imageUrl: "https://images.unsplash.com/photo-1560518883-ce09059eeffa?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=2340&q=80",
          layout: "center"
        },
        {
          type: "text",
          id: "services-intro",
          title: "What We Offer",
          content: "<p>At RealEstate, we provide a comprehensive range of real estate services designed to meet all your property needs. Whether you're buying, selling, renting, or investing, our team of experienced professionals is here to guide you every step of the way.</p><p>Our commitment to excellence and customer satisfaction sets us apart in the industry, ensuring that you receive the highest quality service tailored to your specific requirements.</p>",
          layout: "center"
        },
        {
          type: "features",
          id: "services-features",
          title: "Our Core Services",
          subtitle: "Comprehensive solutions for all your real estate needs",
          columns: 3,
          items: [
            {
              title: "Property Sales",
              description: "Expert guidance through the entire selling process, from valuation to closing.",
              icon: "home"
            },
            {
              title: "Property Acquisition",
              description: "Finding the perfect property that meets your requirements and budget.",
              icon: "search"
            },
            {
              title: "Property Management",
              description: "Comprehensive management services for landlords and property investors.",
              icon: "building"
            },
            {
              title: "Investment Advisory",
              description: "Strategic advice on real estate investments to maximize returns.",
              icon: "trending-up"
            },
            {
              title: "Market Analysis",
              description: "In-depth analysis of market trends and property valuations.",
              icon: "bar-chart"
            },
            {
              title: "Legal Assistance",
              description: "Support with legal aspects of real estate transactions and contracts.",
              icon: "file-text"
            }
          ]
        },
        {
          type: "cta",
          id: "services-cta",
          title: "Ready to Get Started?",
          content: "Contact our team today to discuss how we can help with your real estate needs.",
          buttonText: "Contact Us",
          buttonLink: "/website/contact",
          backgroundColor: "#f8fafc"
        }
      ],
      seo: {
        title: "Our Services | RealEstate",
        description: "Explore our comprehensive range of real estate services including property sales, acquisitions, management, and investment advisory.",
        keywords: "real estate services, property sales, property management, real estate investment"
      }
    },
    faq: {
      id: "faq",
      title: "Frequently Asked Questions",
      slug: "faq",
      description: "Find answers to common questions about real estate",
      enabled: true,
      showInNavigation: true,
      navigationOrder: 4,
      components: [
        {
          type: "hero",
          id: "faq-hero",
          title: "Frequently Asked Questions",
          subtitle: "Find answers to common questions about real estate and our services",
          layout: "center"
        },
        {
          type: "accordion",
          id: "faq-general",
          title: "General Questions",
          items: [
            {
              title: "What services does RealEstate offer?",
              content: "RealEstate offers a comprehensive range of services including property sales, property acquisition, property management, investment advisory, market analysis, and legal assistance for real estate transactions."
            },
            {
              title: "How do I schedule a property viewing?",
              content: "You can schedule a property viewing by contacting our office directly, using the contact form on our website, or by calling the agent listed on the property details page."
            },
            {
              title: "What areas do you serve?",
              content: "We primarily serve the metropolitan area and surrounding suburbs, but our network extends to many regions. Contact us with your specific location to confirm if we provide services in your area."
            },
            {
              title: "Do you handle commercial properties?",
              content: "Yes, we handle both residential and commercial properties. Our team includes specialists in commercial real estate who can assist with office spaces, retail locations, industrial properties, and more."
            }
          ]
        },
        {
          type: "accordion",
          id: "faq-buying",
          title: "Buying Property",
          items: [
            {
              title: "What's the first step in buying a property?",
              content: "The first step is to determine your budget and get pre-approved for a mortgage if needed. Then, contact us to discuss your requirements so we can help you find suitable properties."
            },
            {
              title: "How long does the buying process typically take?",
              content: "The buying process can vary greatly depending on various factors, but typically it takes 30-90 days from making an offer to closing the deal."
            },
            {
              title: "What costs should I expect beyond the purchase price?",
              content: "Additional costs may include closing costs (2-5% of the purchase price), property taxes, insurance, inspection fees, appraisal fees, and possibly homeowner association fees."
            }
          ]
        },
        {
          type: "accordion",
          id: "faq-selling",
          title: "Selling Property",
          items: [
            {
              title: "How do you determine the value of my property?",
              content: "We conduct a comprehensive market analysis that considers recent sales of comparable properties in your area, the condition of your property, current market trends, and unique features of your home."
            },
            {
              title: "What can I do to increase my property's value before selling?",
              content: "Focus on curb appeal, make necessary repairs, consider strategic upgrades to kitchens and bathrooms, ensure the property is clean and decluttered, and possibly consider professional staging."
            },
            {
              title: "How do you market properties?",
              content: "We use a multi-channel marketing approach including professional photography, virtual tours, listings on major real estate portals, social media promotion, our network of buyers, and traditional marketing methods when appropriate."
            }
          ]
        },
        {
          type: "cta",
          id: "faq-cta",
          title: "Still Have Questions?",
          content: "Our team is ready to help with any other questions you might have.",
          buttonText: "Contact Us",
          buttonLink: "/website/contact",
          backgroundColor: "#f8fafc"
        }
      ],
      seo: {
        title: "Frequently Asked Questions | RealEstate",
        description: "Find answers to common questions about buying, selling, and investing in real estate properties.",
        keywords: "real estate FAQ, property buying questions, property selling questions"
      }
    }
  }
};
