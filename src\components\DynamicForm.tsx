import React, { useState } from 'react';
import { useToast } from '@/components/ui/use-toast';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  CardFooter,
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';

interface FormField {
  id: string;
  label: string;
  type: string;
  placeholder?: string;
  required?: boolean;
  options?: Array<{ label: string; value: string }>;
  width?: 'full' | 'half';
  rows?: number;
  order?: number;
}

interface DynamicFormProps {
  id: string;
  title: string;
  description?: string;
  submitButtonText: string;
  successMessage?: string;
  fields: FormField[];
}

const DynamicForm: React.FC<DynamicFormProps> = ({
  id,
  title,
  description,
  submitButtonText,
  successMessage = 'Thank you for your submission!',
  fields,
}) => {
  const { toast } = useToast();
  const [formData, setFormData] = useState<Record<string, any>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);

  // Sort fields by order if provided
  const sortedFields = [...fields].sort((a, b) => 
    (a.order || 999) - (b.order || 999)
  );

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target;
    
    if (type === 'checkbox') {
      setFormData({
        ...formData,
        [name]: (e.target as HTMLInputElement).checked,
      });
    } else {
      setFormData({
        ...formData,
        [name]: value,
      });
    }
  };

  const handleSelectChange = (value: string, fieldId: string) => {
    setFormData({
      ...formData,
      [fieldId]: value,
    });
  };

  const handleCheckboxChange = (checked: boolean, fieldId: string) => {
    setFormData({
      ...formData,
      [fieldId]: checked,
    });
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    // Simulate form submission
    setTimeout(() => {
      console.log('Form submitted:', formData);
      
      toast({
        title: 'Form Submitted',
        description: successMessage,
      });
      
      setIsSubmitting(false);
      setIsSubmitted(true);
      setFormData({});
    }, 1000);
  };

  if (isSubmitted) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Thank You!</CardTitle>
          <CardDescription>Your submission has been received.</CardDescription>
        </CardHeader>
        <CardContent>
          <p>{successMessage}</p>
        </CardContent>
        <CardFooter>
          <Button onClick={() => setIsSubmitted(false)}>Submit Another Response</Button>
        </CardFooter>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>{title}</CardTitle>
        {description && <CardDescription>{description}</CardDescription>}
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {sortedFields.map((field) => {
              const isFullWidth = field.width === 'full';
              
              return (
                <div 
                  key={field.id} 
                  className={isFullWidth ? 'col-span-1 md:col-span-2' : 'col-span-1'}
                >
                  {field.type === 'text' || field.type === 'email' || field.type === 'tel' || field.type === 'number' ? (
                    <div className="space-y-2">
                      <Label htmlFor={field.id}>
                        {field.label} {field.required && <span className="text-red-500">*</span>}
                      </Label>
                      <Input
                        id={field.id}
                        name={field.id}
                        type={field.type}
                        placeholder={field.placeholder}
                        value={formData[field.id] || ''}
                        onChange={handleChange}
                        required={field.required}
                      />
                    </div>
                  ) : field.type === 'textarea' ? (
                    <div className="space-y-2">
                      <Label htmlFor={field.id}>
                        {field.label} {field.required && <span className="text-red-500">*</span>}
                      </Label>
                      <Textarea
                        id={field.id}
                        name={field.id}
                        placeholder={field.placeholder}
                        value={formData[field.id] || ''}
                        onChange={handleChange}
                        required={field.required}
                        rows={field.rows || 4}
                      />
                    </div>
                  ) : field.type === 'select' && field.options ? (
                    <div className="space-y-2">
                      <Label htmlFor={field.id}>
                        {field.label} {field.required && <span className="text-red-500">*</span>}
                      </Label>
                      <Select 
                        value={formData[field.id] || ''} 
                        onValueChange={(value) => handleSelectChange(value, field.id)}
                      >
                        <SelectTrigger id={field.id}>
                          <SelectValue placeholder={field.placeholder || `Select ${field.label}`} />
                        </SelectTrigger>
                        <SelectContent>
                          {field.options.map((option) => (
                            <SelectItem key={option.value} value={option.value}>
                              {option.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  ) : field.type === 'checkbox' ? (
                    <div className="flex items-center space-x-2">
                      <Checkbox 
                        id={field.id} 
                        checked={formData[field.id] || false}
                        onCheckedChange={(checked) => 
                          handleCheckboxChange(checked as boolean, field.id)
                        }
                        required={field.required}
                      />
                      <Label htmlFor={field.id}>
                        {field.label} {field.required && <span className="text-red-500">*</span>}
                      </Label>
                    </div>
                  ) : null}
                </div>
              );
            })}
          </div>
          
          <Button type="submit" className="w-full" disabled={isSubmitting}>
            {isSubmitting ? 'Submitting...' : submitButtonText}
          </Button>
        </form>
      </CardContent>
    </Card>
  );
};

export default DynamicForm;
