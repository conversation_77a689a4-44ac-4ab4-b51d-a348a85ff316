import { useEffect } from 'react';
import { useConfig } from '@/config/ConfigContext';

/**
 * Component that updates the document title based on the site name in the configuration
 */
const DocumentTitle = () => {
  const { config } = useConfig();

  useEffect(() => {
    // Update the document title when the config changes
    if (config.siteName) {
      document.title = config.siteName;
      
      // Also update Open Graph meta tags if they exist
      const ogTitle = document.querySelector('meta[property="og:title"]');
      if (ogTitle) {
        ogTitle.setAttribute('content', config.siteName);
      }
    }
  }, [config.siteName]);

  // This component doesn't render anything
  return null;
};

export default DocumentTitle;
