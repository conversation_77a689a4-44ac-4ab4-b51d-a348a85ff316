
import { use<PERSON><PERSON><PERSON>, Link, Navigate } from 'react-router-dom';
import Layout from '@/components/Layout';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { MapPin, Loader2 } from 'lucide-react';
import Section from '@/components/Section';
import InquiryForm from '@/components/InquiryForm';
import { useProjectById, useProjects } from '@/data/dataService';

const ProjectDetail = () => {
  const { id } = useParams<{ id: string }>();
  const { project, loading, error } = useProjectById(id || '');
  const { projects } = useProjects();

  if (loading) {
    return (
      <Layout>
        <div className="container mx-auto py-16 text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p className="text-gray-500">Loading project details...</p>
        </div>
      </Layout>
    );
  }

  if (error || !project) {
    return <Navigate to="/website/projects" replace />;
  }

  const projectDetails = [
    { name: 'Status', value: project.status },
    { name: 'Location', value: project.location },
    { name: 'Units', value: project.units.toString() },
    { name: 'Completion Date', value: project.completionDate },
    { name: 'Developer', value: 'RealEstate Development Group' },
    { name: 'Architect', value: 'Architectural Innovations Studio' },
  ];

  // Get 3 related projects (excluding current one)
  const relatedProjects = projects
    .filter((p) => p.id !== project.id)
    .slice(0, 3);

  return (
    <Layout>
      <div className="pt-16">
        {/* Project Images */}
        <div className="relative h-[50vh] bg-gray-100">
          <img
            src={project.imageUrl}
            alt={project.title}
            className="w-full h-full object-cover"
          />
        </div>

        <div className="container mx-auto px-4 py-8">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {/* Project Details */}
            <div className="md:col-span-2">
              <div className="mb-6">
                <div className="flex items-center justify-between">
                  <Badge
                    className={`${
                      project.status === 'Upcoming'
                        ? 'bg-blue-500'
                        : project.status === 'Ongoing'
                        ? 'bg-primary'
                        : 'bg-green-600'
                    }`}
                  >
                    {project.status}
                  </Badge>
                  <div className="text-gray-500">Project ID: {project.id}</div>
                </div>
                <h1 className="text-3xl md:text-4xl font-bold mt-2 mb-2">
                  {project.title}
                </h1>
                <div className="flex items-center text-gray-600 mb-4">
                  <MapPin size={18} className="mr-1" />
                  <span>{project.location}</span>
                </div>

                <div className="grid grid-cols-2 gap-4 py-4 border-y border-gray-200">
                  <div>
                    <p className="text-gray-500">Units</p>
                    <p className="font-semibold text-lg">{project.units}</p>
                  </div>
                  <div>
                    <p className="text-gray-500">Completion</p>
                    <p className="font-semibold text-lg">{project.completionDate}</p>
                  </div>
                </div>
              </div>

              <div className="mb-8">
                <h2 className="text-2xl font-semibold mb-4">Project Overview</h2>
                <p className="text-gray-600 mb-4">
                  {project.description}
                </p>
                <p className="text-gray-600 mb-4">
                  This exclusive development offers residents a premium living experience with a range of luxury amenities and thoughtfully designed spaces. The project combines contemporary architecture with sustainable design principles to create a modern and environmentally conscious community.
                </p>
                <p className="text-gray-600">
                  Each unit within {project.title} is crafted with meticulous attention to detail, incorporating high-quality materials and finishes throughout. The development includes a variety of unit sizes and layouts to accommodate different lifestyles and needs.
                </p>
              </div>

              <div className="mb-8">
                <h2 className="text-2xl font-semibold mb-4">Project Details</h2>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                  {projectDetails.map((detail, index) => (
                    <div key={index} className="flex flex-col">
                      <span className="text-gray-500">{detail.name}</span>
                      <span className="font-medium">{detail.value}</span>
                    </div>
                  ))}
                </div>
              </div>

              <div className="mb-8">
                <h2 className="text-2xl font-semibold mb-4">Amenities</h2>
                <div className="grid grid-cols-2 gap-2">
                  <div className="flex items-center">
                    <div className="w-2 h-2 rounded-full bg-primary mr-2"></div>
                    <span>24/7 Security</span>
                  </div>
                  <div className="flex items-center">
                    <div className="w-2 h-2 rounded-full bg-primary mr-2"></div>
                    <span>Fitness Center</span>
                  </div>
                  <div className="flex items-center">
                    <div className="w-2 h-2 rounded-full bg-primary mr-2"></div>
                    <span>Swimming Pool</span>
                  </div>
                  <div className="flex items-center">
                    <div className="w-2 h-2 rounded-full bg-primary mr-2"></div>
                    <span>Landscaped Gardens</span>
                  </div>
                  <div className="flex items-center">
                    <div className="w-2 h-2 rounded-full bg-primary mr-2"></div>
                    <span>Parking Facilities</span>
                  </div>
                  <div className="flex items-center">
                    <div className="w-2 h-2 rounded-full bg-primary mr-2"></div>
                    <span>Community Spaces</span>
                  </div>
                </div>
              </div>

              <div className="mb-8">
                <h2 className="text-2xl font-semibold mb-4">Location</h2>
                <div className="bg-gray-200 h-64 flex items-center justify-center">
                  <p className="text-gray-600">Map would go here in a real application</p>
                </div>
              </div>
            </div>

            {/* Sidebar */}
            <div>
              <div className="sticky top-24">
                <InquiryForm
                  type="project"
                  title="Inquire About This Project"
                  itemName={project.title}
                />

                <div className="mt-6">
                  <Button asChild variant="outline" className="w-full">
                    <Link to="/website/projects">Back to All Projects</Link>
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Related Projects */}
        <Section
          title="Other Projects"
          subtitle="Explore more of our development projects"
          className="bg-gray-50"
        >
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {relatedProjects.map((relatedProject) => (
              <div key={relatedProject.id} className="bg-white rounded-lg shadow-sm overflow-hidden">
                <Link to={`/website/projects/${relatedProject.id}`}>
                  <img
                    src={relatedProject.imageUrl}
                    alt={relatedProject.title}
                    className="w-full h-48 object-cover"
                  />
                </Link>
                <div className="p-4">
                  <Link
                    to={`/website/projects/${relatedProject.id}`}
                    className="text-lg font-semibold hover:text-primary transition"
                  >
                    {relatedProject.title}
                  </Link>
                  <p className="text-gray-600 mt-1">{relatedProject.location}</p>
                  <p className="text-gray-700 mt-2">{relatedProject.status}</p>
                </div>
              </div>
            ))}
          </div>
        </Section>
      </div>
    </Layout>
  );
};

export default ProjectDetail;
