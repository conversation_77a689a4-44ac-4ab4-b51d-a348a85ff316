import React from 'react';
import { CustomPageComponent } from '@/config/types';
import {
  Home, Search, Building, TrendingUp, BarChart, FileText,
  Users, Shield, Clock, Globe, Award, Heart, Star, Zap,
  Map, Phone, Mail, MessageSquare, Settings, Tool
} from 'lucide-react';

interface FeatureItem {
  title: string;
  description: string;
  icon: string;
}

interface FeaturesComponentProps {
  component: CustomPageComponent;
}

const FeaturesComponent: React.FC<FeaturesComponentProps> = ({ component }) => {
  const { title, subtitle, items = [], columns = 3, backgroundColor, textColor } = component;

  console.log('FeaturesComponent rendering with:', { title, subtitle, items, columns });

  const containerStyle = {
    backgroundColor: backgroundColor || 'transparent',
    color: textColor || 'inherit',
  };

  // Map icon names to Lucide icons
  const getIcon = (iconName: string) => {
    const iconMap: Record<string, React.ReactNode> = {
      'home': <Home className="h-8 w-8" />,
      'search': <Search className="h-8 w-8" />,
      'building': <Building className="h-8 w-8" />,
      'trending-up': <TrendingUp className="h-8 w-8" />,
      'bar-chart': <BarChart className="h-8 w-8" />,
      'file-text': <FileText className="h-8 w-8" />,
      'users': <Users className="h-8 w-8" />,
      'shield': <Shield className="h-8 w-8" />,
      'clock': <Clock className="h-8 w-8" />,
      'globe': <Globe className="h-8 w-8" />,
      'award': <Award className="h-8 w-8" />,
      'heart': <Heart className="h-8 w-8" />,
      'star': <Star className="h-8 w-8" />,
      'zap': <Zap className="h-8 w-8" />,
      'map': <Map className="h-8 w-8" />,
      'phone': <Phone className="h-8 w-8" />,
      'mail': <Mail className="h-8 w-8" />,
      'message-square': <MessageSquare className="h-8 w-8" />,
      'settings': <Settings className="h-8 w-8" />,
      'tool': <Tool className="h-8 w-8" />,
    };

    return iconMap[iconName] || <Star className="h-8 w-8" />;
  };

  // Determine grid columns class
  const getGridClass = () => {
    switch (columns) {
      case 1: return 'grid-cols-1';
      case 2: return 'grid-cols-1 md:grid-cols-2';
      case 4: return 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4';
      default: return 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3';
    }
  };

  return (
    <div
      className="py-16"
      style={containerStyle}
    >
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          {title && <h2 className="text-3xl font-bold mb-4">{title}</h2>}
          {subtitle && <p className="text-xl max-w-3xl mx-auto">{subtitle}</p>}
        </div>

        <div className={`grid ${getGridClass()} gap-8`}>
          {items.map((item: FeatureItem, index: number) => (
            <div key={index} className="bg-white p-6 rounded-lg shadow-sm border border-gray-100 hover:shadow-md transition-shadow">
              <div className="text-primary mb-4">
                {getIcon(item.icon)}
              </div>
              <h3 className="text-xl font-semibold mb-2">{item.title}</h3>
              <p className="text-gray-600">{item.description}</p>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default FeaturesComponent;
