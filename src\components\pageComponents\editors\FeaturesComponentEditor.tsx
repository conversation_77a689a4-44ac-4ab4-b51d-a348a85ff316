import React from 'react';
import { CustomPageComponent } from '@/config/types';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Plus, Trash2 } from 'lucide-react';

interface FeatureItem {
  title: string;
  description: string;
  icon: string;
}

interface FeaturesComponentEditorProps {
  component: CustomPageComponent;
  onUpdate: (component: CustomPageComponent) => void;
}

const FeaturesComponentEditor: React.FC<FeaturesComponentEditorProps> = ({ component, onUpdate }) => {
  // Handle component property changes
  const handleChange = (property: string, value: any) => {
    onUpdate({
      ...component,
      [property]: value
    });
  };

  // Handle feature item changes
  const handleFeatureChange = (index: number, field: keyof FeatureItem, value: string) => {
    const updatedItems = [...(component.items || [])];
    updatedItems[index] = {
      ...updatedItems[index],
      [field]: value
    };
    handleChange('items', updatedItems);
  };

  // Add a new feature item
  const addFeatureItem = () => {
    const newItem = {
      title: `Feature ${(component.items?.length || 0) + 1}`,
      description: 'Description goes here',
      icon: 'star'
    };
    handleChange('items', [...(component.items || []), newItem]);
  };

  // Remove a feature item
  const removeFeatureItem = (index: number) => {
    const updatedItems = (component.items || []).filter((_, i) => i !== index);
    handleChange('items', updatedItems);
  };

  // Available icons
  const iconOptions = [
    'home', 'search', 'building', 'trending-up', 'bar-chart', 'file-text',
    'users', 'shield', 'clock', 'globe', 'award', 'heart', 'star', 'zap',
    'map', 'phone', 'mail', 'message-square', 'settings', 'tool'
  ];

  return (
    <div className="space-y-4">
      <div className="space-y-2">
        <Label htmlFor="features-title">Title</Label>
        <Input
          id="features-title"
          value={component.title || ''}
          onChange={(e) => handleChange('title', e.target.value)}
          placeholder="Features Title"
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="features-subtitle">Subtitle</Label>
        <Input
          id="features-subtitle"
          value={component.subtitle || ''}
          onChange={(e) => handleChange('subtitle', e.target.value)}
          placeholder="Features Subtitle"
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="features-columns">Columns</Label>
        <Select
          value={String(component.columns || 3)}
          onValueChange={(value) => handleChange('columns', parseInt(value))}
        >
          <SelectTrigger id="features-columns">
            <SelectValue placeholder="Select number of columns" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="1">1 Column</SelectItem>
            <SelectItem value="2">2 Columns</SelectItem>
            <SelectItem value="3">3 Columns</SelectItem>
            <SelectItem value="4">4 Columns</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div className="space-y-2">
        <Label htmlFor="features-bg-color">Background Color</Label>
        <div className="flex gap-2">
          <Input
            id="features-bg-color"
            type="color"
            value={component.backgroundColor || '#ffffff'}
            onChange={(e) => handleChange('backgroundColor', e.target.value)}
            className="w-12 p-1 h-10"
          />
          <Input
            value={component.backgroundColor || ''}
            onChange={(e) => handleChange('backgroundColor', e.target.value)}
            placeholder="#ffffff or transparent"
            className="flex-1"
          />
        </div>
      </div>

      <div className="space-y-2 mt-6">
        <div className="flex justify-between items-center">
          <Label>Feature Items</Label>
          <Button size="sm" variant="outline" onClick={addFeatureItem}>
            <Plus className="h-4 w-4 mr-2" /> Add Feature
          </Button>
        </div>

        <div className="space-y-4 mt-2">
          {(component.items || []).map((item, index) => (
            <Card key={index} className="overflow-hidden">
              <CardContent className="p-4">
                <div className="flex justify-between items-center mb-2">
                  <h4 className="text-sm font-medium">Feature {index + 1}</h4>
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => removeFeatureItem(index)}
                    title="Remove Feature"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>

                <div className="space-y-3">
                  <div className="space-y-1">
                    <Label htmlFor={`feature-${index}-title`} className="text-xs">Title</Label>
                    <Input
                      id={`feature-${index}-title`}
                      value={item.title || ''}
                      onChange={(e) => handleFeatureChange(index, 'title', e.target.value)}
                      placeholder="Feature Title"
                    />
                  </div>

                  <div className="space-y-1">
                    <Label htmlFor={`feature-${index}-description`} className="text-xs">Description</Label>
                    <Textarea
                      id={`feature-${index}-description`}
                      value={item.description || ''}
                      onChange={(e) => handleFeatureChange(index, 'description', e.target.value)}
                      placeholder="Feature Description"
                      rows={2}
                    />
                  </div>

                  <div className="space-y-1">
                    <Label htmlFor={`feature-${index}-icon`} className="text-xs">Icon</Label>
                    <Select
                      value={item.icon || 'star'}
                      onValueChange={(value) => handleFeatureChange(index, 'icon', value)}
                    >
                      <SelectTrigger id={`feature-${index}-icon`}>
                        <SelectValue placeholder="Select icon" />
                      </SelectTrigger>
                      <SelectContent>
                        {iconOptions.map((icon) => (
                          <SelectItem key={icon} value={icon}>
                            {icon.charAt(0).toUpperCase() + icon.slice(1).replace('-', ' ')}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}

          {(component.items || []).length === 0 && (
            <div className="text-center p-4 border border-dashed rounded-md">
              <p className="text-muted-foreground mb-2">No features added yet</p>
              <Button size="sm" onClick={addFeatureItem}>Add Your First Feature</Button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default FeaturesComponentEditor;
