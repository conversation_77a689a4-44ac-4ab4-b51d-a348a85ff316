
import { Card, CardContent } from '@/components/ui/card';

export interface TestimonialProps {
  quote: string;
  author: string;
  position: string;
  imageUrl?: string;
}

const Testimonial = ({ quote, author, position, imageUrl }: TestimonialProps) => {
  return (
    <Card className="h-full">
      <CardContent className="pt-6">
        <div className="flex flex-col items-center text-center">
          {imageUrl && (
            <div className="w-16 h-16 rounded-full overflow-hidden mb-4">
              <img
                src={imageUrl}
                alt={author}
                className="w-full h-full object-cover"
              />
            </div>
          )}
          
          <p className="text-gray-700 italic mb-4">"{quote}"</p>
          
          <div>
            <p className="font-semibold">{author}</p>
            <p className="text-sm text-gray-500">{position}</p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default Testimonial;
