
import { Button } from '@/components/ui/button';
import { <PERSON> } from 'react-router-dom';

interface HeroSectionProps {
  title: string;
  subtitle: string;
  backgroundImage: string;
  buttonText: string;
  buttonLink: string;
}

const HeroSection = ({
  title,
  subtitle,
  backgroundImage,
  buttonText,
  buttonLink,
}: HeroSectionProps) => {
  return (
    <div
      className="relative h-[85vh] bg-cover bg-center flex items-center"
      style={{ backgroundImage: `url(${backgroundImage})` }}
    >
      <div className="absolute inset-0 bg-black opacity-50" />

      <div className="container mx-auto px-4 relative z-10">
        <div className="max-w-2xl">
          <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-white mb-4 animate-fade-in">
            {title}
          </h1>
          <p className="text-xl text-white/90 mb-8 animate-fade-in">
            {subtitle}
          </p>
          <Button asChild size="lg" className="animate-fade-in">
            <Link to={buttonLink}>{buttonText}</Link>
          </Button>
        </div>
      </div>
    </div>
  );
};

export default HeroSection;
