
import { Link } from 'react-router-dom';
import { useConfig } from '@/config/ConfigContext';

const Footer = () => {
  const { config } = useConfig();
  const currentYear = new Date().getFullYear();

  return (
    <footer className="bg-gray-900 text-white">
      <div className="container mx-auto py-12 px-4">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* Company Info */}
          <div className="col-span-1 md:col-span-2">
            <div className="flex items-center mb-4">
              {config.footerLogoImage ? (
                <img
                  src={config.footerLogoImage}
                  alt={config.logoText}
                  className="h-10 mr-2"
                />
              ) : null}
              <h2 className="text-2xl font-bold">{config.logoText}</h2>
            </div>
            <p className="text-gray-300 mb-4 max-w-md">
              {config.footer.companyInfo.description}
            </p>
            <div className="flex space-x-4">
              {config.footer.socialLinks.filter(link => link.enabled).map((link, index) => (
                <a key={index} href={link.url} className="text-gray-300 hover:text-white transition">
                  {link.name}
                </a>
              ))}
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h3 className="text-lg font-semibold mb-4">{config.footer.quickLinks.title}</h3>
            <ul className="space-y-2">
              {config.footer.quickLinks.links.filter(link => {
                if (!link.enabled) return false;

                // Check if the corresponding page is enabled
                if (link.path.includes('/properties') && !config.propertiesPage.enabled) return false;
                if (link.path.includes('/projects') && !config.projectsPage.enabled) return false;
                if (link.path.includes('/about') && !config.aboutPage.enabled) return false;
                if (link.path.includes('/contact') && !config.contactPage.enabled) return false;

                return true;
              }).map((link, index) => (
                <li key={index}>
                  <Link
                    to={link.path}
                    className="text-gray-300 hover:text-white transition"
                  >
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Contact Info */}
          <div>
            <h3 className="text-lg font-semibold mb-4">Contact Us</h3>
            <ul className="space-y-2">
              {config.footer.contactInfo.address.map((line, index) => (
                <li key={index} className="text-gray-300">{line}</li>
              ))}
              <li className="text-gray-300">Phone: {config.footer.contactInfo.phone}</li>
              <li>
                <a
                  href={`mailto:${config.footer.contactInfo.email}`}
                  className="text-gray-300 hover:text-white transition"
                >
                  {config.footer.contactInfo.email}
                </a>
              </li>
            </ul>
          </div>
        </div>

        {/* Copyright */}
        <div className="border-t border-gray-800 mt-10 pt-6 text-center text-gray-400">
          <p>{config.footer.copyright.replace('2023', currentYear.toString())}</p>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
