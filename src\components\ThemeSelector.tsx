import React from 'react';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Card } from '@/components/ui/card';

interface ThemeSelectorProps {
  value: string;
  onChange: (value: string) => void;
}

const themes = [
  { id: 'light', name: 'Light' },
  { id: 'dark', name: 'Dark' },
  { id: 'blue', name: 'Blue' },
  { id: 'green', name: '<PERSON>' },
  { id: 'purple', name: 'Purple' },
  { id: 'orange', name: 'Orange' },
];

const ThemeSelector: React.FC<ThemeSelectorProps> = ({ value, onChange }) => {
  return (
    <div className="space-y-4">
      <div>
        <Label className="text-base">Website Theme</Label>
        <p className="text-sm text-muted-foreground">
          Select a theme for your website
        </p>
      </div>
      <RadioGroup
        value={value}
        onValueChange={onChange}
        className="grid grid-cols-2 md:grid-cols-3 gap-4"
      >
        {themes.map((theme) => (
          <div key={theme.id}>
            <RadioGroupItem
              value={theme.id}
              id={`theme-${theme.id}`}
              className="peer sr-only"
            />
            <Label
              htmlFor={`theme-${theme.id}`}
              className="flex flex-col items-center justify-between rounded-md border-2 border-muted bg-transparent p-4 hover:bg-accent hover:text-accent-foreground peer-data-[state=checked]:border-primary [&:has([data-state=checked])]:border-primary cursor-pointer"
            >
              <div className={`w-full h-24 rounded-md mb-2 border`} data-theme={theme.id}>
                <div className="flex h-full">
                  <div className="w-1/2 h-full bg-background"></div>
                  <div className="w-1/2 h-full">
                    <div className="h-1/3 bg-primary"></div>
                    <div className="h-1/3 bg-secondary"></div>
                    <div className="h-1/3 bg-accent"></div>
                  </div>
                </div>
              </div>
              <span className="mt-2">{theme.name}</span>
            </Label>
          </div>
        ))}
      </RadioGroup>
    </div>
  );
};

export default ThemeSelector;
