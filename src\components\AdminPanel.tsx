import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useConfig } from '@/config/ConfigContext';
import { useAuth } from '@/auth/AuthContext';
import { WebsiteConfig } from '@/config/types';
import Layout from './Layout';
import { Button } from './ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from './ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card';
import { Checkbox } from './ui/checkbox';
import { Input } from './ui/input';
import { Label } from './ui/label';
import { Textarea } from './ui/textarea';
import { toast } from './ui/use-toast';
import { LogOut } from 'lucide-react';
import ApiConfigLoader from './ApiConfigLoader';
import FormManager from './FormManager';
import ApiConfigPanel from './ApiConfigPanel';
import CustomPageManager from './CustomPageManager';
import ThemeSelector from './ThemeSelector';
import ThemePreview from './ThemePreview';

const AdminPanel = () => {
  const { config, updateConfig, resetConfig } = useConfig();
  const { logout } = useAuth();
  const navigate = useNavigate();
  const [localConfig, setLocalConfig] = useState<WebsiteConfig>(config);
  const [configJson, setConfigJson] = useState<string>('');

  // Update local config when the global config changes
  useEffect(() => {
    console.log('AdminPanel: Global config changed, updating local state');
    setLocalConfig(config);
    setConfigJson(JSON.stringify(config, null, 2));
  }, [config]);



  // Handle saving changes
  const handleSave = () => {
    // Make sure we're working with a clean copy
    const configToSave = JSON.parse(JSON.stringify(localConfig));

    // Log detailed page settings for debugging
    console.log('Saving page settings:', {
      properties: {
        enabled: configToSave.propertiesPage.enabled,
        title: configToSave.propertiesPage.pageHeader.title,
        subtitle: configToSave.propertiesPage.pageHeader.subtitle,
        searchSection: configToSave.propertiesPage.searchSection
      },
      projects: {
        enabled: configToSave.projectsPage.enabled,
        title: configToSave.projectsPage.pageHeader.title,
        subtitle: configToSave.projectsPage.pageHeader.subtitle,
        searchSection: configToSave.projectsPage.searchSection
      },
      about: {
        enabled: configToSave.aboutPage.enabled,
        title: configToSave.aboutPage.pageHeader.title,
        subtitle: configToSave.aboutPage.pageHeader.subtitle,
        ourStory: configToSave.aboutPage.ourStory,
        missionValues: configToSave.aboutPage.missionValues,
        team: configToSave.aboutPage.team,
        callToAction: configToSave.aboutPage.callToAction
      },
      contact: {
        enabled: configToSave.contactPage.enabled,
        title: configToSave.contactPage.pageHeader.title,
        subtitle: configToSave.contactPage.pageHeader.subtitle,
        contactForm: configToSave.contactPage.contactForm,
        contactInfo: configToSave.contactPage.contactInfo
      }
    });

    // Ensure all required objects exist

    // About page sections
    if (!configToSave.aboutPage.missionValues) {
      configToSave.aboutPage.missionValues = {
        enabled: true,
        title: 'Our Mission & Values',
        subtitle: 'The principles that guide our business and relationships'
      };
    }

    if (!configToSave.aboutPage.callToAction) {
      configToSave.aboutPage.callToAction = {
        enabled: true,
        title: 'Work With Us',
        text: 'Whether you\'re looking for your dream home, a property investment, or a development project, our team is ready to help you achieve your real estate goals.',
        buttonText: 'Contact Our Team',
        buttonLink: '/website/contact'
      };
    }

    // Contact page sections
    if (!configToSave.contactPage.contactInfo) {
      configToSave.contactPage.contactInfo = {
        address: [],
        phone: '',
        email: ''
      };
    }

    if (!configToSave.contactPage.contactForm) {
      configToSave.contactPage.contactForm = {
        enabled: true,
        title: 'Send Us a Message',
        submitButtonText: 'Send Message'
      };
    }

    // Update the global config (this will also save to localStorage)
    updateConfig(configToSave);

    toast({
      title: 'Configuration saved',
      description: 'Your changes have been saved successfully.',
    });

    // Log the saved configuration for debugging
    console.log('Saved configuration:', configToSave);
  };

  // Handle resetting to defaults
  const handleReset = () => {
    if (window.confirm('Are you sure you want to reset to default configuration? This cannot be undone.')) {
      resetConfig();
      toast({
        title: 'Configuration reset',
        description: 'Configuration has been reset to defaults.',
      });
    }
  };

  // Handle JSON editor changes
  const handleJsonChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setConfigJson(e.target.value);
    try {
      const parsed = JSON.parse(e.target.value);
      setLocalConfig(parsed);

      // JSON is valid
    } catch (error) {
      // Don't update localConfig if JSON is invalid
    }
  };

  // Handle checkbox changes
  const handleCheckboxChange = (path: (string | number)[], checked: boolean) => {
    // Create a deep copy of the config to avoid mutation issues
    const newConfig = JSON.parse(JSON.stringify(localConfig));
    let current: any = newConfig;

    // Navigate to the nested property
    for (let i = 0; i < path.length - 1; i++) {
      current = current[path[i]];
    }

    // Update the value
    current[path[path.length - 1]] = checked;

    // Log the change for debugging
    console.log(`Changed ${path.join('.')} to ${checked}`);
    console.log('New config:', newConfig);

    // Update local state
    setLocalConfig(newConfig);
    setConfigJson(JSON.stringify(newConfig, null, 2));


  };

  // Handle text input changes
  const handleTextChange = (path: (string | number)[], value: string) => {
    // Create a deep copy of the config to avoid mutation issues
    const newConfig = JSON.parse(JSON.stringify(localConfig));
    let current: any = newConfig;

    // Navigate to the nested property
    for (let i = 0; i < path.length - 1; i++) {
      current = current[path[i]];
    }

    // Update the value
    current[path[path.length - 1]] = value;

    // Update local state
    setLocalConfig(newConfig);
    setConfigJson(JSON.stringify(newConfig, null, 2));


  };

  // Handle array changes
  const handleArrayChange = (path: (string | number)[], value: any[]) => {
    // Create a deep copy of the config to avoid mutation issues
    const newConfig = JSON.parse(JSON.stringify(localConfig));
    let current: any = newConfig;

    // Navigate to the nested property
    for (let i = 0; i < path.length - 1; i++) {
      current = current[path[i]];
    }

    // Update the value
    current[path[path.length - 1]] = value;

    // Update local state
    setLocalConfig(newConfig);
    setConfigJson(JSON.stringify(newConfig, null, 2));


  };

  return (
    <Layout>
      <div className="container mx-auto py-8">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-3xl font-bold">Website Configuration</h1>
          <Button
            variant="outline"
            size="sm"
            onClick={() => {
              logout();
              navigate('/website/login');
              toast({
                title: 'Logged out',
                description: 'You have been logged out successfully',
              });
            }}
            className="flex items-center gap-2"
          >
            <LogOut size={16} />
            Logout
          </Button>
        </div>

        <Tabs defaultValue="general">
          <TabsList className="mb-4">
            <TabsTrigger value="general">General</TabsTrigger>
            <TabsTrigger value="pages">Pages</TabsTrigger>
            <TabsTrigger value="sections">Sections</TabsTrigger>
            <TabsTrigger value="forms">Forms</TabsTrigger>
            <TabsTrigger value="custom-pages">Custom Pages</TabsTrigger>
            <TabsTrigger value="json">JSON Editor</TabsTrigger>
          </TabsList>

          {/* General Settings */}
          <TabsContent value="general">
            <Card>
              <CardHeader>
                <CardTitle>General Settings</CardTitle>
                <CardDescription>Configure basic website settings</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="siteName">Site Name (Browser Title)</Label>
                    <Input
                      id="siteName"
                      value={localConfig.siteName}
                      onChange={(e) => handleTextChange(['siteName'], e.target.value)}
                    />
                    <p className="text-xs text-gray-500 mt-1">Used for the browser tab title only</p>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="logoText">Logo Text</Label>
                    <Input
                      id="logoText"
                      value={localConfig.logoText}
                      onChange={(e) => handleTextChange(['logoText'], e.target.value)}
                    />
                    <p className="text-xs text-gray-500 mt-1">Used for the logo in header and footer</p>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                  <div className="space-y-2">
                    <Label htmlFor="headerLogoImage">Header Logo Image URL</Label>
                    <Input
                      id="headerLogoImage"
                      value={localConfig.headerLogoImage || ''}
                      onChange={(e) => handleTextChange(['headerLogoImage'], e.target.value)}
                      placeholder="https://example.com/logo.png"
                    />
                    <p className="text-xs text-gray-500 mt-1">Image to display in the header (optional)</p>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="footerLogoImage">Footer Logo Image URL</Label>
                    <Input
                      id="footerLogoImage"
                      value={localConfig.footerLogoImage || ''}
                      onChange={(e) => handleTextChange(['footerLogoImage'], e.target.value)}
                      placeholder="https://example.com/logo.png"
                    />
                    <p className="text-xs text-gray-500 mt-1">Image to display in the footer (optional)</p>
                  </div>
                </div>

                <div className="border-t pt-4 mt-4">
                  <ThemeSelector
                    value={localConfig.theme || 'light'}
                    onChange={(value) => handleTextChange(['theme'], value)}
                  />
                  <div className="mt-6">
                    <ThemePreview theme={localConfig.theme || 'light'} />
                  </div>
                </div>

                <div className="border-t pt-4">
                  <h3 className="text-lg font-medium mb-3">Navigation</h3>

                  <div className="space-y-4">
                    <div>
                      <h4 className="text-sm font-medium mb-2">Navigation Links</h4>
                      {localConfig.navigation.links.map((link, index) => (
                        <div key={index} className="flex items-center gap-3 mb-2 p-2 border rounded-md">
                          <Checkbox
                            id={`nav-link-${index}`}
                            checked={link.enabled}
                            onCheckedChange={(checked) => {
                              if (checked !== undefined) {
                                handleCheckboxChange(['navigation', 'links', index, 'enabled'], checked as boolean);
                              }
                            }}
                          />
                          <div className="grid grid-cols-2 gap-2 flex-1">
                            <Input
                              placeholder="Link Text"
                              value={link.name}
                              onChange={(e) => handleTextChange(['navigation', 'links', index, 'name'], e.target.value)}
                            />
                            <Input
                              placeholder="Link Path"
                              value={link.path}
                              onChange={(e) => handleTextChange(['navigation', 'links', index, 'path'], e.target.value)}
                            />
                          </div>
                        </div>
                      ))}
                    </div>

                    <div className="space-y-2">
                      <h4 className="text-sm font-medium mb-2">CTA Button</h4>
                      <div className="flex items-center gap-2 mb-2">
                        <Checkbox
                          id="ctaButtonEnabled"
                          checked={localConfig.navigation.ctaButton.enabled}
                          onCheckedChange={(checked) => {
                            if (checked !== undefined) {
                              handleCheckboxChange(['navigation', 'ctaButton', 'enabled'], checked as boolean);
                            }
                          }}
                        />
                        <Label htmlFor="ctaButtonEnabled">Enable CTA Button</Label>
                      </div>

                      {localConfig.navigation.ctaButton.enabled && (
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                          <div className="space-y-1">
                            <Label htmlFor="ctaButtonText">Button Text</Label>
                            <Input
                              id="ctaButtonText"
                              value={localConfig.navigation.ctaButton.text}
                              onChange={(e) => handleTextChange(['navigation', 'ctaButton', 'text'], e.target.value)}
                            />
                          </div>
                          <div className="space-y-1">
                            <Label htmlFor="ctaButtonLink">Button Link</Label>
                            <Input
                              id="ctaButtonLink"
                              value={localConfig.navigation.ctaButton.link}
                              onChange={(e) => handleTextChange(['navigation', 'ctaButton', 'link'], e.target.value)}
                            />
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                <div className="border-t pt-4">
                  <h3 className="text-lg font-medium mb-3">Footer Settings</h3>

                  <div className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="footerDescription">Company Description</Label>
                      <Textarea
                        id="footerDescription"
                        value={localConfig.footer.companyInfo.description}
                        onChange={(e) => handleTextChange(['footer', 'companyInfo', 'description'], e.target.value)}
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="footerCopyright">Copyright Text</Label>
                      <Input
                        id="footerCopyright"
                        value={localConfig.footer.copyright}
                        onChange={(e) => handleTextChange(['footer', 'copyright'], e.target.value)}
                      />
                    </div>

                    <div className="space-y-2">
                      <h4 className="text-sm font-medium mb-2">Contact Information</h4>

                      <div className="space-y-2">
                        <Label htmlFor="footerAddress">Address (one line per entry)</Label>
                        <Textarea
                          id="footerAddress"
                          value={localConfig.footer.contactInfo.address.join('\n')}
                          onChange={(e) => {
                            const addressLines = e.target.value.split('\n');
                            handleArrayChange(['footer', 'contactInfo', 'address'], addressLines);
                          }}
                        />
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                        <div className="space-y-1">
                          <Label htmlFor="footerPhone">Phone</Label>
                          <Input
                            id="footerPhone"
                            value={localConfig.footer.contactInfo.phone}
                            onChange={(e) => handleTextChange(['footer', 'contactInfo', 'phone'], e.target.value)}
                          />
                        </div>
                        <div className="space-y-1">
                          <Label htmlFor="footerEmail">Email</Label>
                          <Input
                            id="footerEmail"
                            value={localConfig.footer.contactInfo.email}
                            onChange={(e) => handleTextChange(['footer', 'contactInfo', 'email'], e.target.value)}
                          />
                        </div>
                      </div>
                    </div>

                    <div className="mb-4">
                      <h4 className="text-sm font-medium mb-2">Social Links</h4>
                      {localConfig.footer.socialLinks.map((link, index) => (
                        <div key={index} className="flex items-center gap-3 mb-2 p-2 border rounded-md">
                          <Checkbox
                            id={`social-link-${index}`}
                            checked={link.enabled}
                            onCheckedChange={(checked) => {
                              if (checked !== undefined) {
                                handleCheckboxChange(['footer', 'socialLinks', index, 'enabled'], checked as boolean);
                              }
                            }}
                          />
                          <div className="grid grid-cols-2 gap-2 flex-1">
                            <Input
                              placeholder="Platform Name"
                              value={link.name}
                              onChange={(e) => handleTextChange(['footer', 'socialLinks', index, 'name'], e.target.value)}
                            />
                            <Input
                              placeholder="URL"
                              value={link.url}
                              onChange={(e) => handleTextChange(['footer', 'socialLinks', index, 'url'], e.target.value)}
                            />
                          </div>
                        </div>
                      ))}
                    </div>

                    <div>
                      <div className="flex items-center justify-between mb-2">
                        <h4 className="text-sm font-medium">Footer Navigation Links</h4>
                        <div className="text-xs text-gray-500">
                          <Label htmlFor="footerLinksTitle" className="mr-2">Section Title:</Label>
                          <Input
                            id="footerLinksTitle"
                            value={localConfig.footer.quickLinks.title}
                            onChange={(e) => handleTextChange(['footer', 'quickLinks', 'title'], e.target.value)}
                            className="inline-block w-40 h-7"
                          />
                        </div>
                      </div>

                      {localConfig.footer.quickLinks.links.map((link, index) => (
                        <div key={index} className="flex items-center gap-3 mb-2 p-2 border rounded-md">
                          <Checkbox
                            id={`footer-link-${index}`}
                            checked={link.enabled}
                            onCheckedChange={(checked) => {
                              if (checked !== undefined) {
                                handleCheckboxChange(['footer', 'quickLinks', 'links', index, 'enabled'], checked as boolean);
                              }
                            }}
                          />
                          <div className="grid grid-cols-2 gap-2 flex-1">
                            <Input
                              placeholder="Link Text"
                              value={link.name}
                              onChange={(e) => handleTextChange(['footer', 'quickLinks', 'links', index, 'name'], e.target.value)}
                            />
                            <Input
                              placeholder="Link Path"
                              value={link.path}
                              onChange={(e) => handleTextChange(['footer', 'quickLinks', 'links', index, 'path'], e.target.value)}
                            />
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Pages Settings */}
          <TabsContent value="pages">
            <Card>
              <CardHeader>
                <CardTitle>Page Settings</CardTitle>
                <CardDescription>Enable or disable pages and configure their settings</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-1 gap-6">
                  {/* Properties Page */}
                  <div className="border p-4 rounded-md">
                    <div className="flex items-center space-x-2 mb-4">
                      <Checkbox
                        id="propertiesEnabled"
                        checked={localConfig.propertiesPage.enabled}
                        onCheckedChange={(checked) => {
                          if (checked !== undefined) {
                            handleCheckboxChange(['propertiesPage', 'enabled'], checked as boolean);
                          }
                        }}
                      />
                      <Label htmlFor="propertiesEnabled" className="text-lg font-medium">Properties Page</Label>
                    </div>

                    {localConfig.propertiesPage.enabled && (
                      <div className="space-y-4">
                        <div className="border-t pt-3">
                          <h4 className="text-sm font-medium mb-2">Page Header</h4>
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                            <div className="space-y-2">
                              <Label htmlFor="propertiesTitle">Page Title</Label>
                              <Input
                                id="propertiesTitle"
                                value={localConfig.propertiesPage.pageHeader.title}
                                onChange={(e) => handleTextChange(
                                  ['propertiesPage', 'pageHeader', 'title'],
                                  e.target.value
                                )}
                              />
                            </div>
                            <div className="space-y-2">
                              <Label htmlFor="propertiesSubtitle">Page Subtitle</Label>
                              <Input
                                id="propertiesSubtitle"
                                value={localConfig.propertiesPage.pageHeader.subtitle}
                                onChange={(e) => handleTextChange(
                                  ['propertiesPage', 'pageHeader', 'subtitle'],
                                  e.target.value
                                )}
                              />
                            </div>
                          </div>
                          <div className="mt-3">
                            <Label htmlFor="propertiesBgImage">Background Image URL</Label>
                            <Input
                              id="propertiesBgImage"
                              value={localConfig.propertiesPage.pageHeader.backgroundImage}
                              onChange={(e) => handleTextChange(
                                ['propertiesPage', 'pageHeader', 'backgroundImage'],
                                e.target.value
                              )}
                            />
                          </div>
                        </div>

                        <div className="border-t pt-3">
                          <div className="flex items-center space-x-2 mb-2">
                            <Checkbox
                              id="propertiesSearchEnabled"
                              checked={localConfig.propertiesPage.searchSection.enabled}
                              onCheckedChange={(checked) => {
                                if (checked !== undefined) {
                                  handleCheckboxChange(['propertiesPage', 'searchSection', 'enabled'], checked as boolean);
                                }
                              }}
                            />
                            <Label htmlFor="propertiesSearchEnabled">Search Section</Label>
                          </div>

                          {localConfig.propertiesPage.searchSection.enabled && (
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-3 mt-2">
                              <div className="space-y-2">
                                <Label htmlFor="propertiesSearchTitle">Section Title</Label>
                                <Input
                                  id="propertiesSearchTitle"
                                  value={localConfig.propertiesPage.searchSection.title}
                                  onChange={(e) => handleTextChange(
                                    ['propertiesPage', 'searchSection', 'title'],
                                    e.target.value
                                  )}
                                />
                              </div>
                              <div className="space-y-2">
                                <Label htmlFor="propertiesSearchSubtitle">Section Subtitle</Label>
                                <Input
                                  id="propertiesSearchSubtitle"
                                  value={localConfig.propertiesPage.searchSection.subtitle || ''}
                                  onChange={(e) => handleTextChange(
                                    ['propertiesPage', 'searchSection', 'subtitle'],
                                    e.target.value
                                  )}
                                />
                              </div>
                            </div>
                          )}
                        </div>
                      </div>
                    )}
                  </div>

                  {/* Projects Page */}
                  <div className="border p-4 rounded-md">
                    <div className="flex items-center space-x-2 mb-4">
                      <Checkbox
                        id="projectsEnabled"
                        checked={localConfig.projectsPage.enabled}
                        onCheckedChange={(checked) => {
                          if (checked !== undefined) {
                            handleCheckboxChange(['projectsPage', 'enabled'], checked as boolean);
                          }
                        }}
                      />
                      <Label htmlFor="projectsEnabled" className="text-lg font-medium">Projects Page</Label>
                    </div>

                    {localConfig.projectsPage.enabled && (
                      <div className="space-y-4">
                        <div className="border-t pt-3">
                          <h4 className="text-sm font-medium mb-2">Page Header</h4>
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                            <div className="space-y-2">
                              <Label htmlFor="projectsTitle">Page Title</Label>
                              <Input
                                id="projectsTitle"
                                value={localConfig.projectsPage.pageHeader.title}
                                onChange={(e) => handleTextChange(
                                  ['projectsPage', 'pageHeader', 'title'],
                                  e.target.value
                                )}
                              />
                            </div>
                            <div className="space-y-2">
                              <Label htmlFor="projectsSubtitle">Page Subtitle</Label>
                              <Input
                                id="projectsSubtitle"
                                value={localConfig.projectsPage.pageHeader.subtitle}
                                onChange={(e) => handleTextChange(
                                  ['projectsPage', 'pageHeader', 'subtitle'],
                                  e.target.value
                                )}
                              />
                            </div>
                          </div>
                          <div className="mt-3">
                            <Label htmlFor="projectsBgImage">Background Image URL</Label>
                            <Input
                              id="projectsBgImage"
                              value={localConfig.projectsPage.pageHeader.backgroundImage}
                              onChange={(e) => handleTextChange(
                                ['projectsPage', 'pageHeader', 'backgroundImage'],
                                e.target.value
                              )}
                            />
                          </div>
                        </div>

                        <div className="border-t pt-3">
                          <div className="flex items-center space-x-2 mb-2">
                            <Checkbox
                              id="projectsSearchEnabled"
                              checked={localConfig.projectsPage.searchSection.enabled}
                              onCheckedChange={(checked) => {
                                if (checked !== undefined) {
                                  handleCheckboxChange(['projectsPage', 'searchSection', 'enabled'], checked as boolean);
                                }
                              }}
                            />
                            <Label htmlFor="projectsSearchEnabled">Search Section</Label>
                          </div>

                          {localConfig.projectsPage.searchSection.enabled && (
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-3 mt-2">
                              <div className="space-y-2">
                                <Label htmlFor="projectsSearchTitle">Section Title</Label>
                                <Input
                                  id="projectsSearchTitle"
                                  value={localConfig.projectsPage.searchSection.title}
                                  onChange={(e) => handleTextChange(
                                    ['projectsPage', 'searchSection', 'title'],
                                    e.target.value
                                  )}
                                />
                              </div>
                              <div className="space-y-2">
                                <Label htmlFor="projectsSearchSubtitle">Section Subtitle</Label>
                                <Input
                                  id="projectsSearchSubtitle"
                                  value={localConfig.projectsPage.searchSection.subtitle || ''}
                                  onChange={(e) => handleTextChange(
                                    ['projectsPage', 'searchSection', 'subtitle'],
                                    e.target.value
                                  )}
                                />
                              </div>
                            </div>
                          )}
                        </div>
                      </div>
                    )}
                  </div>

                  {/* About Page */}
                  <div className="border p-4 rounded-md">
                    <div className="flex items-center space-x-2 mb-4">
                      <Checkbox
                        id="aboutEnabled"
                        checked={localConfig.aboutPage.enabled}
                        onCheckedChange={(checked) => {
                          if (checked !== undefined) {
                            handleCheckboxChange(['aboutPage', 'enabled'], checked as boolean);
                          }
                        }}
                      />
                      <Label htmlFor="aboutEnabled" className="text-lg font-medium">About Page</Label>
                    </div>

                    {localConfig.aboutPage.enabled && (
                      <div className="space-y-4">
                        <div className="border-t pt-3">
                          <h4 className="text-sm font-medium mb-2">Page Header</h4>
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                            <div className="space-y-2">
                              <Label htmlFor="aboutTitle">Page Title</Label>
                              <Input
                                id="aboutTitle"
                                value={localConfig.aboutPage.pageHeader.title}
                                onChange={(e) => handleTextChange(
                                  ['aboutPage', 'pageHeader', 'title'],
                                  e.target.value
                                )}
                              />
                            </div>
                            <div className="space-y-2">
                              <Label htmlFor="aboutSubtitle">Page Subtitle</Label>
                              <Input
                                id="aboutSubtitle"
                                value={localConfig.aboutPage.pageHeader.subtitle}
                                onChange={(e) => handleTextChange(
                                  ['aboutPage', 'pageHeader', 'subtitle'],
                                  e.target.value
                                )}
                              />
                            </div>
                          </div>
                          <div className="mt-3">
                            <Label htmlFor="aboutBgImage">Background Image URL</Label>
                            <Input
                              id="aboutBgImage"
                              value={localConfig.aboutPage.pageHeader.backgroundImage}
                              onChange={(e) => handleTextChange(
                                ['aboutPage', 'pageHeader', 'backgroundImage'],
                                e.target.value
                              )}
                            />
                          </div>
                        </div>

                        <div className="border-t pt-3">
                          <div className="flex items-center space-x-2 mb-2">
                            <Checkbox
                              id="ourStoryEnabled"
                              checked={localConfig.aboutPage.ourStory.enabled}
                              onCheckedChange={(checked) => {
                                if (checked !== undefined) {
                                  handleCheckboxChange(['aboutPage', 'ourStory', 'enabled'], checked as boolean);
                                }
                              }}
                            />
                            <Label htmlFor="ourStoryEnabled">Our Story Section</Label>
                          </div>

                          {localConfig.aboutPage.ourStory.enabled && (
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-3 mt-2">
                              <div className="space-y-2">
                                <Label htmlFor="ourStoryTitle">Section Title</Label>
                                <Input
                                  id="ourStoryTitle"
                                  value={localConfig.aboutPage.ourStory.title}
                                  onChange={(e) => handleTextChange(
                                    ['aboutPage', 'ourStory', 'title'],
                                    e.target.value
                                  )}
                                />
                              </div>
                              <div className="space-y-2">
                                <Label htmlFor="ourStorySubtitle">Section Subtitle</Label>
                                <Input
                                  id="ourStorySubtitle"
                                  value={localConfig.aboutPage.ourStory.subtitle || ''}
                                  onChange={(e) => handleTextChange(
                                    ['aboutPage', 'ourStory', 'subtitle'],
                                    e.target.value
                                  )}
                                />
                              </div>
                            </div>
                          )}
                        </div>

                        <div className="border-t pt-3">
                          <div className="flex items-center space-x-2 mb-2">
                            <Checkbox
                              id="teamSectionEnabled"
                              checked={localConfig.aboutPage.team.enabled}
                              onCheckedChange={(checked) => {
                                if (checked !== undefined) {
                                  handleCheckboxChange(['aboutPage', 'team', 'enabled'], checked as boolean);
                                }
                              }}
                            />
                            <Label htmlFor="teamSectionEnabled">Team Section</Label>
                          </div>

                          {localConfig.aboutPage.team.enabled && (
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-3 mt-2">
                              <div className="space-y-2">
                                <Label htmlFor="teamSectionTitle">Section Title</Label>
                                <Input
                                  id="teamSectionTitle"
                                  value={localConfig.aboutPage.team.title}
                                  onChange={(e) => handleTextChange(
                                    ['aboutPage', 'team', 'title'],
                                    e.target.value
                                  )}
                                />
                              </div>
                              <div className="space-y-2">
                                <Label htmlFor="teamSectionSubtitle">Section Subtitle</Label>
                                <Input
                                  id="teamSectionSubtitle"
                                  value={localConfig.aboutPage.team.subtitle || ''}
                                  onChange={(e) => handleTextChange(
                                    ['aboutPage', 'team', 'subtitle'],
                                    e.target.value
                                  )}
                                />
                              </div>
                            </div>
                          )}
                        </div>

                        <div className="border-t pt-3">
                          <div className="flex items-center space-x-2 mb-2">
                            <Checkbox
                              id="missionValuesEnabled"
                              checked={localConfig.aboutPage.missionValues.enabled}
                              onCheckedChange={(checked) => {
                                if (checked !== undefined) {
                                  handleCheckboxChange(['aboutPage', 'missionValues', 'enabled'], checked as boolean);
                                }
                              }}
                            />
                            <Label htmlFor="missionValuesEnabled">Mission & Values Section</Label>
                          </div>

                          {localConfig.aboutPage.missionValues.enabled && (
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-3 mt-2">
                              <div className="space-y-2">
                                <Label htmlFor="missionValuesTitle">Section Title</Label>
                                <Input
                                  id="missionValuesTitle"
                                  value={localConfig.aboutPage.missionValues.title}
                                  onChange={(e) => handleTextChange(
                                    ['aboutPage', 'missionValues', 'title'],
                                    e.target.value
                                  )}
                                />
                              </div>
                              <div className="space-y-2">
                                <Label htmlFor="missionValuesSubtitle">Section Subtitle</Label>
                                <Input
                                  id="missionValuesSubtitle"
                                  value={localConfig.aboutPage.missionValues.subtitle || ''}
                                  onChange={(e) => handleTextChange(
                                    ['aboutPage', 'missionValues', 'subtitle'],
                                    e.target.value
                                  )}
                                />
                              </div>
                            </div>
                          )}
                        </div>

                        <div className="border-t pt-3">
                          <div className="flex items-center space-x-2 mb-2">
                            <Checkbox
                              id="callToActionEnabled"
                              checked={localConfig.aboutPage.callToAction.enabled}
                              onCheckedChange={(checked) => {
                                if (checked !== undefined) {
                                  handleCheckboxChange(['aboutPage', 'callToAction', 'enabled'], checked as boolean);
                                }
                              }}
                            />
                            <Label htmlFor="callToActionEnabled">Call to Action Section</Label>
                          </div>

                          {localConfig.aboutPage.callToAction.enabled && (
                            <div className="space-y-3 mt-2">
                              <div className="space-y-2">
                                <Label htmlFor="callToActionTitle">Section Title</Label>
                                <Input
                                  id="callToActionTitle"
                                  value={localConfig.aboutPage.callToAction.title}
                                  onChange={(e) => handleTextChange(
                                    ['aboutPage', 'callToAction', 'title'],
                                    e.target.value
                                  )}
                                />
                              </div>

                              <div className="space-y-2">
                                <Label htmlFor="callToActionText">Section Text</Label>
                                <Textarea
                                  id="callToActionText"
                                  value={localConfig.aboutPage.callToAction.text}
                                  onChange={(e) => handleTextChange(
                                    ['aboutPage', 'callToAction', 'text'],
                                    e.target.value
                                  )}
                                />
                              </div>

                              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                                <div className="space-y-2">
                                  <Label htmlFor="callToActionButtonText">Button Text</Label>
                                  <Input
                                    id="callToActionButtonText"
                                    value={localConfig.aboutPage.callToAction.buttonText}
                                    onChange={(e) => handleTextChange(
                                      ['aboutPage', 'callToAction', 'buttonText'],
                                      e.target.value
                                    )}
                                  />
                                </div>
                                <div className="space-y-2">
                                  <Label htmlFor="callToActionButtonLink">Button Link</Label>
                                  <Input
                                    id="callToActionButtonLink"
                                    value={localConfig.aboutPage.callToAction.buttonLink}
                                    onChange={(e) => handleTextChange(
                                      ['aboutPage', 'callToAction', 'buttonLink'],
                                      e.target.value
                                    )}
                                  />
                                </div>
                              </div>
                            </div>
                          )}
                        </div>
                      </div>
                    )}
                  </div>

                  {/* Contact Page */}
                  <div className="border p-4 rounded-md">
                    <div className="flex items-center space-x-2 mb-4">
                      <Checkbox
                        id="contactEnabled"
                        checked={localConfig.contactPage.enabled}
                        onCheckedChange={(checked) => {
                          if (checked !== undefined) {
                            handleCheckboxChange(['contactPage', 'enabled'], checked as boolean);
                          }
                        }}
                      />
                      <Label htmlFor="contactEnabled" className="text-lg font-medium">Contact Page</Label>
                    </div>

                    {localConfig.contactPage.enabled && (
                      <div className="space-y-4">
                        <div className="border-t pt-3">
                          <h4 className="text-sm font-medium mb-2">Page Header</h4>
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                            <div className="space-y-2">
                              <Label htmlFor="contactTitle">Page Title</Label>
                              <Input
                                id="contactTitle"
                                value={localConfig.contactPage.pageHeader.title}
                                onChange={(e) => handleTextChange(
                                  ['contactPage', 'pageHeader', 'title'],
                                  e.target.value
                                )}
                              />
                            </div>
                            <div className="space-y-2">
                              <Label htmlFor="contactSubtitle">Page Subtitle</Label>
                              <Input
                                id="contactSubtitle"
                                value={localConfig.contactPage.pageHeader.subtitle}
                                onChange={(e) => handleTextChange(
                                  ['contactPage', 'pageHeader', 'subtitle'],
                                  e.target.value
                                )}
                              />
                            </div>
                          </div>
                          <div className="mt-3">
                            <Label htmlFor="contactBgImage">Background Image URL</Label>
                            <Input
                              id="contactBgImage"
                              value={localConfig.contactPage.pageHeader.backgroundImage}
                              onChange={(e) => handleTextChange(
                                ['contactPage', 'pageHeader', 'backgroundImage'],
                                e.target.value
                              )}
                            />
                          </div>
                        </div>

                        <div className="border-t pt-3">
                          <div className="flex items-center space-x-2 mb-2">
                            <Checkbox
                              id="contactFormEnabled"
                              checked={localConfig.contactPage.contactForm.enabled}
                              onCheckedChange={(checked) => {
                                if (checked !== undefined) {
                                  handleCheckboxChange(['contactPage', 'contactForm', 'enabled'], checked as boolean);
                                }
                              }}
                            />
                            <Label htmlFor="contactFormEnabled">Contact Form</Label>
                          </div>

                          {localConfig.contactPage.contactForm.enabled && (
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-3 mt-2">
                              <div className="space-y-2">
                                <Label htmlFor="contactFormTitle">Form Title</Label>
                                <Input
                                  id="contactFormTitle"
                                  value={localConfig.contactPage.contactForm.title}
                                  onChange={(e) => handleTextChange(
                                    ['contactPage', 'contactForm', 'title'],
                                    e.target.value
                                  )}
                                />
                              </div>
                              <div className="space-y-2">
                                <Label htmlFor="contactFormButtonText">Submit Button Text</Label>
                                <Input
                                  id="contactFormButtonText"
                                  value={localConfig.contactPage.contactForm.submitButtonText || 'Send Message'}
                                  onChange={(e) => handleTextChange(
                                    ['contactPage', 'contactForm', 'submitButtonText'],
                                    e.target.value
                                  )}
                                />
                              </div>
                            </div>
                          )}
                        </div>

                        <div className="border-t pt-3">
                          <h4 className="text-sm font-medium mb-2">Contact Information</h4>
                          <div className="space-y-2">
                            <Label htmlFor="contactAddress">Address (one line per entry)</Label>
                            <Textarea
                              id="contactAddress"
                              value={(localConfig.contactPage.contactInfo.address || []).join('\n')}
                              onChange={(e) => {
                                const addressLines = e.target.value.split('\n');
                                handleArrayChange(['contactPage', 'contactInfo', 'address'], addressLines);
                              }}
                            />
                          </div>

                          <div className="grid grid-cols-1 md:grid-cols-2 gap-3 mt-3">
                            <div className="space-y-2">
                              <Label htmlFor="contactPhone">Phone</Label>
                              <Input
                                id="contactPhone"
                                value={localConfig.contactPage.contactInfo.phone || ''}
                                onChange={(e) => handleTextChange(
                                  ['contactPage', 'contactInfo', 'phone'],
                                  e.target.value
                                )}
                              />
                            </div>
                            <div className="space-y-2">
                              <Label htmlFor="contactEmail">Email</Label>
                              <Input
                                id="contactEmail"
                                value={localConfig.contactPage.contactInfo.email || ''}
                                onChange={(e) => handleTextChange(
                                  ['contactPage', 'contactInfo', 'email'],
                                  e.target.value
                                )}
                              />
                            </div>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Sections Settings */}
          <TabsContent value="sections">
            <Card>
              <CardHeader>
                <CardTitle>Home Page Sections</CardTitle>
                <CardDescription>Enable or disable sections on the home page</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-1 gap-6">
                  {/* Hero Section */}
                  <div className="border p-4 rounded-md">
                    <div className="flex items-center space-x-2 mb-4">
                      <Checkbox
                        id="heroEnabled"
                        checked={localConfig.homePage.hero.enabled}
                        onCheckedChange={(checked) =>
                          handleCheckboxChange(['homePage', 'hero', 'enabled'], checked as boolean)
                        }
                      />
                      <Label htmlFor="heroEnabled" className="text-lg font-medium">Hero Section</Label>
                    </div>

                    {localConfig.homePage.hero.enabled && (
                      <div className="space-y-4">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                          <div className="space-y-2">
                            <Label htmlFor="heroTitle">Title</Label>
                            <Input
                              id="heroTitle"
                              value={localConfig.homePage.hero.title}
                              onChange={(e) => handleTextChange(
                                ['homePage', 'hero', 'title'],
                                e.target.value
                              )}
                            />
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="heroSubtitle">Subtitle</Label>
                            <Input
                              id="heroSubtitle"
                              value={localConfig.homePage.hero.subtitle}
                              onChange={(e) => handleTextChange(
                                ['homePage', 'hero', 'subtitle'],
                                e.target.value
                              )}
                            />
                          </div>
                        </div>

                        <div className="space-y-2">
                          <Label htmlFor="heroBackgroundImage">Background Image URL</Label>
                          <Input
                            id="heroBackgroundImage"
                            value={localConfig.homePage.hero.backgroundImage}
                            onChange={(e) => handleTextChange(
                              ['homePage', 'hero', 'backgroundImage'],
                              e.target.value
                            )}
                          />
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                          <div className="space-y-2">
                            <Label htmlFor="heroButtonText">Button Text</Label>
                            <Input
                              id="heroButtonText"
                              value={localConfig.homePage.hero.buttonText}
                              onChange={(e) => handleTextChange(
                                ['homePage', 'hero', 'buttonText'],
                                e.target.value
                              )}
                            />
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="heroButtonLink">Button Link</Label>
                            <Input
                              id="heroButtonLink"
                              value={localConfig.homePage.hero.buttonLink}
                              onChange={(e) => handleTextChange(
                                ['homePage', 'hero', 'buttonLink'],
                                e.target.value
                              )}
                            />
                          </div>
                        </div>
                      </div>
                    )}
                  </div>

                  {/* Featured Properties Section */}
                  <div className="border p-4 rounded-md">
                    <div className="flex items-center space-x-2 mb-4">
                      <Checkbox
                        id="featuredPropertiesEnabled"
                        checked={localConfig.homePage.featuredProperties.enabled}
                        onCheckedChange={(checked) =>
                          handleCheckboxChange(['homePage', 'featuredProperties', 'enabled'], checked as boolean)
                        }
                      />
                      <Label htmlFor="featuredPropertiesEnabled">Featured Properties Section</Label>
                    </div>

                    {localConfig.homePage.featuredProperties.enabled && (
                      <div className="space-y-2">
                        <Label htmlFor="featuredPropertiesTitle">Title</Label>
                        <Input
                          id="featuredPropertiesTitle"
                          value={localConfig.homePage.featuredProperties.title}
                          onChange={(e) => handleTextChange(
                            ['homePage', 'featuredProperties', 'title'],
                            e.target.value
                          )}
                        />

                        <Label htmlFor="featuredPropertiesSubtitle">Subtitle</Label>
                        <Input
                          id="featuredPropertiesSubtitle"
                          value={localConfig.homePage.featuredProperties.subtitle || ''}
                          onChange={(e) => handleTextChange(
                            ['homePage', 'featuredProperties', 'subtitle'],
                            e.target.value
                          )}
                        />
                      </div>
                    )}
                  </div>

                  {/* Featured Projects Section */}
                  <div className="border p-4 rounded-md">
                    <div className="flex items-center space-x-2 mb-4">
                      <Checkbox
                        id="featuredProjectsEnabled"
                        checked={localConfig.homePage.featuredProjects.enabled}
                        onCheckedChange={(checked) =>
                          handleCheckboxChange(['homePage', 'featuredProjects', 'enabled'], checked as boolean)
                        }
                      />
                      <Label htmlFor="featuredProjectsEnabled">Featured Projects Section</Label>
                    </div>

                    {localConfig.homePage.featuredProjects.enabled && (
                      <div className="space-y-2">
                        <Label htmlFor="featuredProjectsTitle">Title</Label>
                        <Input
                          id="featuredProjectsTitle"
                          value={localConfig.homePage.featuredProjects.title}
                          onChange={(e) => handleTextChange(
                            ['homePage', 'featuredProjects', 'title'],
                            e.target.value
                          )}
                        />

                        <Label htmlFor="featuredProjectsSubtitle">Subtitle</Label>
                        <Input
                          id="featuredProjectsSubtitle"
                          value={localConfig.homePage.featuredProjects.subtitle || ''}
                          onChange={(e) => handleTextChange(
                            ['homePage', 'featuredProjects', 'subtitle'],
                            e.target.value
                          )}
                        />
                      </div>
                    )}
                  </div>

                  {/* Why Choose Us Section */}
                  <div className="border p-4 rounded-md">
                    <div className="flex items-center space-x-2 mb-4">
                      <Checkbox
                        id="whyChooseUsEnabled"
                        checked={localConfig.homePage.whyChooseUs.enabled}
                        onCheckedChange={(checked) =>
                          handleCheckboxChange(['homePage', 'whyChooseUs', 'enabled'], checked as boolean)
                        }
                      />
                      <Label htmlFor="whyChooseUsEnabled">Why Choose Us Section</Label>
                    </div>

                    {localConfig.homePage.whyChooseUs.enabled && (
                      <div className="space-y-2">
                        <Label htmlFor="whyChooseUsTitle">Title</Label>
                        <Input
                          id="whyChooseUsTitle"
                          value={localConfig.homePage.whyChooseUs.title}
                          onChange={(e) => handleTextChange(
                            ['homePage', 'whyChooseUs', 'title'],
                            e.target.value
                          )}
                        />

                        <Label htmlFor="whyChooseUsSubtitle">Subtitle</Label>
                        <Input
                          id="whyChooseUsSubtitle"
                          value={localConfig.homePage.whyChooseUs.subtitle}
                          onChange={(e) => handleTextChange(
                            ['homePage', 'whyChooseUs', 'subtitle'],
                            e.target.value
                          )}
                        />
                      </div>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>

            <h3 className="text-lg font-medium mt-8 mb-4">Data API Configuration</h3>
            <div className="grid grid-cols-1 gap-8">
              <ApiConfigPanel type="properties" />
              <ApiConfigPanel type="projects" />
            </div>
          </TabsContent>

          {/* Custom Pages Manager */}
          <TabsContent value="custom-pages">
            <CustomPageManager />
          </TabsContent>

          {/* JSON Editor */}
          <TabsContent value="json">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              <div className="lg:col-span-2">
                <Card>
                  <CardHeader>
                    <CardTitle>JSON Configuration Editor</CardTitle>
                    <CardDescription>Edit the raw JSON configuration</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <Textarea
                      className="font-mono h-[500px]"
                      value={configJson}
                      onChange={handleJsonChange}
                    />
                  </CardContent>
                </Card>
              </div>

              <div>
                <ApiConfigLoader />

                <Card className="mt-6">
                  <CardHeader>
                    <CardTitle>Configuration Actions</CardTitle>
                    <CardDescription>Manage your configuration</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <Button
                      variant="outline"
                      className="w-full"
                      onClick={() => {
                        const dataStr = "data:text/json;charset=utf-8," + encodeURIComponent(configJson);
                        const downloadAnchorNode = document.createElement('a');
                        downloadAnchorNode.setAttribute("href", dataStr);
                        downloadAnchorNode.setAttribute("download", `website-config-${new Date().toISOString().slice(0, 10)}.json`);
                        document.body.appendChild(downloadAnchorNode);
                        downloadAnchorNode.click();
                        downloadAnchorNode.remove();
                      }}
                    >
                      Export Configuration
                    </Button>

                    <Button
                      variant="destructive"
                      className="w-full"
                      onClick={handleReset}
                    >
                      Reset to Default
                    </Button>
                  </CardContent>
                </Card>
              </div>
            </div>
          </TabsContent>
        </Tabs>

        <div className="mt-6 border-t pt-4">
          <div className="flex items-center justify-between">
            <p className="text-sm text-gray-500">
              <strong>Note:</strong> Changes are not saved automatically. Click the Save Changes button to apply your changes.
            </p>
            <div className="flex space-x-4">
              <Button variant="outline" onClick={handleReset}>
                Reset to Defaults
              </Button>
              <Button onClick={handleSave} size="lg">
                Save Changes
              </Button>
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default AdminPanel;
