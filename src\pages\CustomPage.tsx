import React, { useEffect } from 'react';
import { useParams, Navigate } from 'react-router-dom';
import { useConfig } from '@/config/ConfigContext';
import Layout from '@/components/Layout';
import PageComponentRenderer from '@/components/pageComponents/PageComponentRenderer';
import { CustomPageConfig } from '@/config/types';

const CustomPage: React.FC = () => {
  const { slug } = useParams<{ slug: string }>();
  const { config } = useConfig();

  console.log('CustomPage rendering with slug:', slug);
  console.log('Config:', config);

  // Find the page by slug
  const customPages = config.customPages || {};
  console.log('Available custom pages:', customPages);

  const page = Object.values(customPages).find(
    (page: CustomPageConfig) => page.slug === slug && page.enabled
  );

  console.log('Found page:', page);

  // Set page title and meta tags
  useEffect(() => {
    if (page) {
      // Set page title
      document.title = page.seo?.title || `${page.title} | ${config.siteName}`;

      // Set meta description
      const metaDescription = document.querySelector('meta[name="description"]');
      if (metaDescription) {
        metaDescription.setAttribute('content', page.seo?.description || page.description || '');
      }

      // Set meta keywords
      const metaKeywords = document.querySelector('meta[name="keywords"]');
      if (metaKeywords && page.seo?.keywords) {
        metaKeywords.setAttribute('content', page.seo.keywords);
      }

      // Set og:image if available
      const ogImage = document.querySelector('meta[property="og:image"]');
      if (ogImage && page.seo?.ogImage) {
        ogImage.setAttribute('content', page.seo.ogImage);
      }
    }
  }, [page, config.siteName]);

  // If page not found, redirect to home
  if (!page) {
    return <Navigate to="/website" replace />;
  }

  return (
    <Layout>
      {/* Render each component in the page */}
      <div className="custom-page-container">
        {page.components && page.components.length > 0 ? (
          page.components.map((component) => (
            <PageComponentRenderer key={component.id} component={component} />
          ))
        ) : (
          <div className="py-16 text-center">
            <h2 className="text-2xl font-bold mb-4">No components found</h2>
            <p className="text-gray-500">This page has no content components.</p>
          </div>
        )}
      </div>
    </Layout>
  );
};

export default CustomPage;
