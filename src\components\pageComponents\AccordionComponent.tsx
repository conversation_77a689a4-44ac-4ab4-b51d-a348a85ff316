import React from 'react';
import { CustomPageComponent } from '@/config/types';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';

interface AccordionItem {
  title: string;
  content: string;
}

interface AccordionComponentProps {
  component: CustomPageComponent;
}

const AccordionComponent: React.FC<AccordionComponentProps> = ({ component }) => {
  const { title, subtitle, items = [], backgroundColor, textColor } = component;

  console.log('AccordionComponent rendering with:', { title, subtitle, items });

  const containerStyle = {
    backgroundColor: backgroundColor || 'transparent',
    color: textColor || 'inherit',
  };

  return (
    <div
      className="py-16"
      style={containerStyle}
    >
      <div className="container mx-auto px-4">
        <div className="max-w-3xl mx-auto">
          {title && (
            <div className="text-center mb-8">
              <h2 className="text-3xl font-bold mb-2">{title}</h2>
              {subtitle && <p className="text-xl">{subtitle}</p>}
            </div>
          )}

          <Accordion type="single" collapsible className="w-full">
            {items.map((item: AccordionItem, index: number) => (
              <AccordionItem key={index} value={`item-${index}`}>
                <AccordionTrigger className="text-left text-lg font-medium">
                  {item.title}
                </AccordionTrigger>
                <AccordionContent>
                  <div className="pt-2 pb-4 text-gray-600">
                    {item.content}
                  </div>
                </AccordionContent>
              </AccordionItem>
            ))}
          </Accordion>
        </div>
      </div>
    </div>
  );
};

export default AccordionComponent;
