import React from 'react';
import { CustomPageComponent } from '@/config/types';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

interface HeroComponentEditorProps {
  component: CustomPageComponent;
  onUpdate: (component: CustomPageComponent) => void;
}

const HeroComponentEditor: React.FC<HeroComponentEditorProps> = ({ component, onUpdate }) => {
  // Handle component property changes
  const handleChange = (property: string, value: any) => {
    onUpdate({
      ...component,
      [property]: value
    });
  };

  return (
    <div className="space-y-4">
      <div className="space-y-2">
        <Label htmlFor="hero-title">Title</Label>
        <Input
          id="hero-title"
          value={component.title || ''}
          onChange={(e) => handleChange('title', e.target.value)}
          placeholder="Hero Title"
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="hero-subtitle">Subtitle</Label>
        <Input
          id="hero-subtitle"
          value={component.subtitle || ''}
          onChange={(e) => handleChange('subtitle', e.target.value)}
          placeholder="Hero Subtitle"
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="hero-image">Background Image URL</Label>
        <Input
          id="hero-image"
          value={component.imageUrl || ''}
          onChange={(e) => handleChange('imageUrl', e.target.value)}
          placeholder="https://example.com/image.jpg"
        />
        <p className="text-xs text-muted-foreground">
          Leave blank for no background image
        </p>
      </div>

      <div className="space-y-2">
        <Label htmlFor="hero-layout">Text Alignment</Label>
        <Select
          value={component.layout || 'center'}
          onValueChange={(value) => handleChange('layout', value)}
        >
          <SelectTrigger id="hero-layout">
            <SelectValue placeholder="Select alignment" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="left">Left</SelectItem>
            <SelectItem value="center">Center</SelectItem>
            <SelectItem value="right">Right</SelectItem>
          </SelectContent>
        </Select>
      </div>
    </div>
  );
};

export default HeroComponentEditor;
