import { PropertyProps } from '@/components/PropertyCard';
import { ProjectProps } from '@/components/ProjectCard';
import { properties as staticProperties } from './properties';
import { projects as staticProjects } from './projects';
import { fetchFromApi } from '@/services/apiService';
import { useConfig } from '@/config/ConfigContext';
import { useState, useEffect } from 'react';

/**
 * Custom hook to fetch properties from API or static data
 * @returns Properties data and loading state
 */
export const useProperties = () => {
  const { config } = useConfig();
  const [properties, setProperties] = useState<PropertyProps[]>(staticProperties);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    const fetchProperties = async () => {
      // If API is not enabled, use static data
      if (!config.propertiesPage.api?.enabled) {
        return;
      }

      setLoading(true);
      setError(null);

      try {
        const apiData = await fetchFromApi<PropertyProps>(config.propertiesPage.api);
        
        // Transform API data to match PropertyProps interface
        const transformedData: PropertyProps[] = apiData.map(item => ({
          id: item.id || `prop-${Math.random().toString(36).substr(2, 9)}`,
          title: item.title || 'Untitled Property',
          location: item.location || 'Unknown Location',
          price: item.price || 'Price on request',
          beds: typeof item.beds === 'number' ? item.beds : 0,
          baths: typeof item.baths === 'number' ? item.baths : 0,
          sqft: typeof item.sqft === 'number' ? item.sqft : 0,
          imageUrl: item.image || 'https://placehold.co/600x400?text=No+Image',
          status: (item.status as 'For Sale' | 'For Rent' | 'Sold') || 'For Sale',
        }));

        setProperties(transformedData);
      } catch (err) {
        console.error('Error fetching properties:', err);
        setError(err instanceof Error ? err : new Error('Failed to fetch properties'));
        // Fallback to static data
        setProperties(staticProperties);
      } finally {
        setLoading(false);
      }
    };

    fetchProperties();
  }, [config.propertiesPage.api]);

  return { properties, loading, error };
};

/**
 * Custom hook to fetch projects from API or static data
 * @returns Projects data and loading state
 */
export const useProjects = () => {
  const { config } = useConfig();
  const [projects, setProjects] = useState<ProjectProps[]>(staticProjects);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    const fetchProjects = async () => {
      // If API is not enabled, use static data
      if (!config.projectsPage.api?.enabled) {
        return;
      }

      setLoading(true);
      setError(null);

      try {
        const apiData = await fetchFromApi<ProjectProps>(config.projectsPage.api);
        
        // Transform API data to match ProjectProps interface
        const transformedData: ProjectProps[] = apiData.map(item => ({
          id: item.id || `proj-${Math.random().toString(36).substr(2, 9)}`,
          title: item.title || 'Untitled Project',
          location: item.location || 'Unknown Location',
          description: item.description || 'No description available',
          imageUrl: item.image || 'https://placehold.co/600x400?text=No+Image',
          status: (item.status as 'Upcoming' | 'Ongoing' | 'Completed') || 'Ongoing',
          units: typeof item.units === 'number' ? item.units : 0,
          completionDate: item.completionDate || 'TBD',
        }));

        setProjects(transformedData);
      } catch (err) {
        console.error('Error fetching projects:', err);
        setError(err instanceof Error ? err : new Error('Failed to fetch projects'));
        // Fallback to static data
        setProjects(staticProjects);
      } finally {
        setLoading(false);
      }
    };

    fetchProjects();
  }, [config.projectsPage.api]);

  return { projects, loading, error };
};

/**
 * Get featured properties (either from API or static data)
 * @param count Number of featured properties to return
 * @returns Array of featured properties
 */
export const useFeaturedProperties = (count: number = 3) => {
  const { properties, loading, error } = useProperties();
  
  const featuredProperties = properties.slice(0, count);
  
  return { featuredProperties, loading, error };
};

/**
 * Get featured projects (either from API or static data)
 * @param count Number of featured projects to return
 * @returns Array of featured projects
 */
export const useFeaturedProjects = (count: number = 3) => {
  const { projects, loading, error } = useProjects();
  
  const featuredProjects = projects.slice(0, count);
  
  return { featuredProjects, loading, error };
};

/**
 * Get a property by ID (either from API or static data)
 * @param id Property ID
 * @returns Property object or undefined if not found
 */
export const usePropertyById = (id: string) => {
  const { properties, loading, error } = useProperties();
  
  const property = properties.find(property => property.id === id);
  
  return { property, loading, error };
};

/**
 * Get a project by ID (either from API or static data)
 * @param id Project ID
 * @returns Project object or undefined if not found
 */
export const useProjectById = (id: string) => {
  const { projects, loading, error } = useProjects();
  
  const project = projects.find(project => project.id === id);
  
  return { project, loading, error };
};
