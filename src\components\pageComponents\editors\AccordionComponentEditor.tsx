import React from 'react';
import { CustomPageComponent } from '@/config/types';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Plus, Trash2, MoveUp, MoveDown } from 'lucide-react';

interface AccordionItem {
  title: string;
  content: string;
}

interface AccordionComponentEditorProps {
  component: CustomPageComponent;
  onUpdate: (component: CustomPageComponent) => void;
}

const AccordionComponentEditor: React.FC<AccordionComponentEditorProps> = ({ component, onUpdate }) => {
  // Handle component property changes
  const handleChange = (property: string, value: any) => {
    onUpdate({
      ...component,
      [property]: value
    });
  };

  // Handle accordion item changes
  const handleItemChange = (index: number, field: keyof AccordionItem, value: string) => {
    const updatedItems = [...(component.items || [])];
    updatedItems[index] = {
      ...updatedItems[index],
      [field]: value
    };
    handleChange('items', updatedItems);
  };

  // Add a new accordion item
  const addAccordionItem = () => {
    const newItem = {
      title: `Question ${(component.items?.length || 0) + 1}`,
      content: 'Answer goes here'
    };
    handleChange('items', [...(component.items || []), newItem]);
  };

  // Remove an accordion item
  const removeAccordionItem = (index: number) => {
    const updatedItems = (component.items || []).filter((_, i) => i !== index);
    handleChange('items', updatedItems);
  };

  // Move an accordion item up
  const moveItemUp = (index: number) => {
    if (index === 0) return;
    
    const updatedItems = [...(component.items || [])];
    const temp = updatedItems[index];
    updatedItems[index] = updatedItems[index - 1];
    updatedItems[index - 1] = temp;
    
    handleChange('items', updatedItems);
  };

  // Move an accordion item down
  const moveItemDown = (index: number) => {
    const items = component.items || [];
    if (index === items.length - 1) return;
    
    const updatedItems = [...items];
    const temp = updatedItems[index];
    updatedItems[index] = updatedItems[index + 1];
    updatedItems[index + 1] = temp;
    
    handleChange('items', updatedItems);
  };

  return (
    <div className="space-y-4">
      <div className="space-y-2">
        <Label htmlFor="accordion-title">Title</Label>
        <Input
          id="accordion-title"
          value={component.title || ''}
          onChange={(e) => handleChange('title', e.target.value)}
          placeholder="Accordion Title"
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="accordion-subtitle">Subtitle</Label>
        <Input
          id="accordion-subtitle"
          value={component.subtitle || ''}
          onChange={(e) => handleChange('subtitle', e.target.value)}
          placeholder="Accordion Subtitle"
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="accordion-bg-color">Background Color</Label>
        <div className="flex gap-2">
          <Input
            id="accordion-bg-color"
            type="color"
            value={component.backgroundColor || '#ffffff'}
            onChange={(e) => handleChange('backgroundColor', e.target.value)}
            className="w-12 p-1 h-10"
          />
          <Input
            value={component.backgroundColor || ''}
            onChange={(e) => handleChange('backgroundColor', e.target.value)}
            placeholder="#ffffff or transparent"
            className="flex-1"
          />
        </div>
      </div>

      <div className="space-y-2 mt-6">
        <div className="flex justify-between items-center">
          <Label>Accordion Items</Label>
          <Button size="sm" variant="outline" onClick={addAccordionItem}>
            <Plus className="h-4 w-4 mr-2" /> Add Item
          </Button>
        </div>

        <div className="space-y-4 mt-2">
          {(component.items || []).map((item, index) => (
            <Card key={index} className="overflow-hidden">
              <CardContent className="p-4">
                <div className="flex justify-between items-center mb-2">
                  <h4 className="text-sm font-medium">Item {index + 1}</h4>
                  <div className="flex gap-1">
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => moveItemUp(index)}
                      disabled={index === 0}
                      title="Move Up"
                    >
                      <MoveUp className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => moveItemDown(index)}
                      disabled={index === (component.items?.length || 0) - 1}
                      title="Move Down"
                    >
                      <MoveDown className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => removeAccordionItem(index)}
                      title="Remove Item"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>

                <div className="space-y-3">
                  <div className="space-y-1">
                    <Label htmlFor={`accordion-${index}-title`} className="text-xs">Question</Label>
                    <Input
                      id={`accordion-${index}-title`}
                      value={item.title || ''}
                      onChange={(e) => handleItemChange(index, 'title', e.target.value)}
                      placeholder="Question"
                    />
                  </div>

                  <div className="space-y-1">
                    <Label htmlFor={`accordion-${index}-content`} className="text-xs">Answer</Label>
                    <Textarea
                      id={`accordion-${index}-content`}
                      value={item.content || ''}
                      onChange={(e) => handleItemChange(index, 'content', e.target.value)}
                      placeholder="Answer"
                      rows={3}
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}

          {(component.items || []).length === 0 && (
            <div className="text-center p-4 border border-dashed rounded-md">
              <p className="text-muted-foreground mb-2">No items added yet</p>
              <Button size="sm" onClick={addAccordionItem}>Add Your First Item</Button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default AccordionComponentEditor;
