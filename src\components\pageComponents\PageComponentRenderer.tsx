import React from 'react';
import { CustomPageComponent } from '@/config/types';

// Lazy load components to avoid circular dependencies
const HeroComponent = React.lazy(() => import('./HeroComponent'));
const TextComponent = React.lazy(() => import('./TextComponent'));
const FeaturesComponent = React.lazy(() => import('./FeaturesComponent'));
const CtaComponent = React.lazy(() => import('./CtaComponent'));
const AccordionComponent = React.lazy(() => import('./AccordionComponent'));

interface PageComponentRendererProps {
  component: CustomPageComponent;
}

const PageComponentRenderer: React.FC<PageComponentRendererProps> = ({ component }) => {
  const { type } = component;

  console.log('Rendering component:', type, component);

  // Fallback component for loading state
  const LoadingFallback = () => (
    <div className="py-8 px-4 bg-gray-100 text-center">
      <p className="text-gray-500">Loading component...</p>
    </div>
  );

  // Render the appropriate component based on type
  return (
    <React.Suspense fallback={<LoadingFallback />}>
      {(() => {
        switch (type) {
          case 'hero':
            return <HeroComponent component={component} />;
          case 'text':
            return <TextComponent component={component} />;
          case 'features':
            return <FeaturesComponent component={component} />;
          case 'cta':
            return <CtaComponent component={component} />;
          case 'accordion':
            return <AccordionComponent component={component} />;
          default:
            return (
              <div className="py-8 px-4 bg-gray-100 text-center">
                <p className="text-gray-500">Unknown component type: {type}</p>
              </div>
            );
        }
      })()}
    </React.Suspense>
  );
};

export default PageComponentRenderer;
