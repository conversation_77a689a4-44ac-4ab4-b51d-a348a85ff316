import React from 'react';
import { CustomPageComponent } from '@/config/types';

interface TextComponentProps {
  component: CustomPageComponent;
}

const TextComponent: React.FC<TextComponentProps> = ({ component }) => {
  const { title, subtitle, content, layout = 'left', backgroundColor, textColor } = component;

  console.log('TextComponent rendering with:', { title, subtitle, content, layout });

  const containerStyle = {
    backgroundColor: backgroundColor || 'transparent',
    color: textColor || 'inherit',
  };

  // Determine text alignment class
  let alignmentClass = 'text-left';
  if (layout === 'center') alignmentClass = 'text-center';
  if (layout === 'right') alignmentClass = 'text-right';

  return (
    <div
      className="py-16"
      style={containerStyle}
    >
      <div className="container mx-auto px-4">
        <div className={`max-w-4xl mx-auto ${alignmentClass}`}>
          {title && <h2 className="text-3xl font-bold mb-4">{title}</h2>}
          {subtitle && <p className="text-xl mb-6">{subtitle}</p>}
          {content && (
            <div
              className="prose prose-lg max-w-none"
              dangerouslySetInnerHTML={{ __html: content }}
            />
          )}
        </div>
      </div>
    </div>
  );
};

export default TextComponent;
