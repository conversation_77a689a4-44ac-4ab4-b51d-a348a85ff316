
import { useState } from 'react';
import Layout from '@/components/Layout';
import PageHeader from '@/components/PageHeader';
import Section from '@/components/Section';
import ProjectCard from '@/components/ProjectCard';
import { useProjects } from '@/data/dataService';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Search, Loader2 } from 'lucide-react';
import { useConfig } from '@/config/ConfigContext';
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
  PaginationNumber,
  PaginationEllipsis
} from '@/components/ui/pagination';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';

const Projects = () => {
  const { config } = useConfig();
  const { projects, loading, error } = useProjects();
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('All');
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);

  const filteredProjects = projects.filter((project) => {
    const matchesSearch = project.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                          project.location.toLowerCase().includes(searchTerm.toLowerCase()) ||
                          project.description.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesStatus = statusFilter === 'All' || project.status === statusFilter;

    return matchesSearch && matchesStatus;
  });

  const totalPages = Math.ceil(filteredProjects.length / pageSize);
  const startIndex = (currentPage - 1) * pageSize;
  const paginatedProjects = filteredProjects.slice(startIndex, startIndex + pageSize);

  const handlePageChange = (page: number) => {
    if (page > 0 && page <= totalPages) {
      setCurrentPage(page);
      window.scrollTo({ top: 0, behavior: 'smooth' });
    }
  };

  const renderPaginationNumbers = () => {
    const pageNumbers = [];

    if (totalPages <= 6) {
      // Show all page numbers if there are 6 or fewer pages
      for (let i = 1; i <= totalPages; i++) {
        pageNumbers.push(
          <PaginationNumber
            key={i}
            page={i}
            current={currentPage}
            onClick={() => handlePageChange(i)}
          />
        );
      }
    } else {
      // Always show first page
      pageNumbers.push(
        <PaginationNumber
          key={1}
          page={1}
          current={currentPage}
          onClick={() => handlePageChange(1)}
        />
      );

      // Show ellipsis if current page is more than 3
      if (currentPage > 3) {
        pageNumbers.push(<PaginationEllipsis key="ellipsis-1" />);
      }

      // Show pages around current page
      const startPage = Math.max(2, currentPage - 1);
      const endPage = Math.min(totalPages - 1, currentPage + 1);

      for (let i = startPage; i <= endPage; i++) {
        pageNumbers.push(
          <PaginationNumber
            key={i}
            page={i}
            current={currentPage}
            onClick={() => handlePageChange(i)}
          />
        );
      }

      // Show ellipsis if current page is less than totalPages-2
      if (currentPage < totalPages - 2) {
        pageNumbers.push(<PaginationEllipsis key="ellipsis-2" />);
      }

      // Always show last page
      pageNumbers.push(
        <PaginationNumber
          key={totalPages}
          page={totalPages}
          current={currentPage}
          onClick={() => handlePageChange(totalPages)}
        />
      );
    }

    return pageNumbers;
  };

  return (
    <Layout>
      <PageHeader
        title={config.projectsPage.pageHeader.title}
        subtitle={config.projectsPage.pageHeader.subtitle}
        backgroundImage={config.projectsPage.pageHeader.backgroundImage}
      />

      <Section
        title={config.projectsPage.searchSection.title}
        subtitle={config.projectsPage.searchSection.subtitle}
      >
        <div className="max-w-4xl mx-auto">
          <div className="flex flex-col md:flex-row gap-4 mb-8">
            <div className="relative flex-grow">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
              <Input
                placeholder="Search by location or project name"
                className="pl-10"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>

            <div className="flex gap-2">
              <Button
                variant={statusFilter === 'All' ? 'default' : 'outline'}
                onClick={() => setStatusFilter('All')}
              >
                All
              </Button>
              <Button
                variant={statusFilter === 'Upcoming' ? 'default' : 'outline'}
                onClick={() => setStatusFilter('Upcoming')}
              >
                Upcoming
              </Button>
              <Button
                variant={statusFilter === 'Ongoing' ? 'default' : 'outline'}
                onClick={() => setStatusFilter('Ongoing')}
              >
                Ongoing
              </Button>
              <Button
                variant={statusFilter === 'Completed' ? 'default' : 'outline'}
                onClick={() => setStatusFilter('Completed')}
              >
                Completed
              </Button>
            </div>
          </div>

          <div className="flex justify-end mb-4">
            <div className="flex items-center gap-2">
              <span className="text-sm text-gray-600">Items per page:</span>
              <Select
                value={pageSize.toString()}
                onValueChange={(value) => {
                  setPageSize(Number(value));
                  setCurrentPage(1);
                }}
              >
                <SelectTrigger className="w-[100px] h-9">
                  <SelectValue placeholder="10" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="10">10</SelectItem>
                  <SelectItem value="25">25</SelectItem>
                  <SelectItem value="50">50</SelectItem>
                  <SelectItem value="100">100</SelectItem>
                  <SelectItem value="200">200</SelectItem>
                  <SelectItem value="500">500</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {loading ? (
            <div className="text-center py-16">
              <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
              <p className="text-gray-500">Loading projects...</p>
            </div>
          ) : error ? (
            <div className="text-center py-8 text-red-500">
              <p>Error loading projects. Please try again later.</p>
              <p className="text-sm mt-2">{error.message}</p>
            </div>
          ) : filteredProjects.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-gray-500">No projects match your search criteria.</p>
            </div>
          ) : (
            <>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                {paginatedProjects.map((project) => (
                  <ProjectCard key={project.id} {...project} />
                ))}
              </div>

              {totalPages > 1 && (
                <div className="mt-8">
                  <Pagination>
                    <PaginationContent>
                      <PaginationItem>
                        <PaginationPrevious
                          onClick={() => handlePageChange(currentPage - 1)}
                          className={currentPage === 1 ? "pointer-events-none opacity-50" : ""}
                        />
                      </PaginationItem>

                      {renderPaginationNumbers()}

                      <PaginationItem>
                        <PaginationNext
                          onClick={() => handlePageChange(currentPage + 1)}
                          className={currentPage === totalPages ? "pointer-events-none opacity-50" : ""}
                        />
                      </PaginationItem>
                    </PaginationContent>
                  </Pagination>

                  <div className="mt-2 text-center text-sm text-gray-600">
                    Showing {startIndex + 1}-{Math.min(startIndex + pageSize, filteredProjects.length)} of {filteredProjects.length} projects
                  </div>
                </div>
              )}
            </>
          )}
        </div>
      </Section>
    </Layout>
  );
};

export default Projects;
