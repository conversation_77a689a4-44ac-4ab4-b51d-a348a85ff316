import React, { useState } from 'react';
import { useConfig } from '@/config/ConfigContext';
import { CustomPageConfig } from '@/config/types';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { useToast } from '@/components/ui/use-toast';
import { 
  Edit, 
  Eye, 
  ArrowUpDown,
  Plus,
  Trash2,
  Copy,
  ExternalLink
} from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import CustomPageEditor from './CustomPageEditor';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogClose,
} from '@/components/ui/dialog';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { v4 as uuidv4 } from 'uuid';

const CustomPageManager: React.FC = () => {
  const { config, updateConfig } = useConfig();
  const { toast } = useToast();
  const [editingPageId, setEditingPageId] = useState<string | null>(null);
  const [previewPageId, setPreviewPageId] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [sortField, setSortField] = useState<keyof CustomPageConfig>('navigationOrder');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');
  const [isCreatingPage, setIsCreatingPage] = useState(false);
  const [deletePageId, setDeletePageId] = useState<string | null>(null);
  const [duplicatePageId, setDuplicatePageId] = useState<string | null>(null);
  const [newPageTitle, setNewPageTitle] = useState('');
  const [newPageSlug, setNewPageSlug] = useState('');

  // Get pages from config
  const customPages = config.customPages || {};
  
  // Convert pages object to array for sorting and filtering
  const pagesArray = Object.values(customPages);
  
  // Filter pages based on search term
  const filteredPages = pagesArray.filter(page => 
    page.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    page.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
    page.slug.toLowerCase().includes(searchTerm.toLowerCase()) ||
    page.description?.toLowerCase().includes(searchTerm.toLowerCase())
  );
  
  // Sort pages
  const sortedPages = [...filteredPages].sort((a, b) => {
    const aValue = a[sortField];
    const bValue = b[sortField];
    
    if (aValue === undefined) return sortDirection === 'asc' ? 1 : -1;
    if (bValue === undefined) return sortDirection === 'asc' ? -1 : 1;
    
    if (typeof aValue === 'string' && typeof bValue === 'string') {
      return sortDirection === 'asc' 
        ? aValue.localeCompare(bValue)
        : bValue.localeCompare(aValue);
    }
    
    if (typeof aValue === 'number' && typeof bValue === 'number') {
      return sortDirection === 'asc' ? aValue - bValue : bValue - aValue;
    }
    
    if (typeof aValue === 'boolean' && typeof bValue === 'boolean') {
      return sortDirection === 'asc' 
        ? (aValue === bValue ? 0 : aValue ? -1 : 1)
        : (aValue === bValue ? 0 : aValue ? 1 : -1);
    }
    
    return 0;
  });

  // Handle sorting
  const handleSort = (field: keyof CustomPageConfig) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  // Toggle page enabled state
  const handleTogglePageEnabled = (pageId: string, enabled: boolean) => {
    const page = customPages[pageId];
    if (!page) return;
    
    const updatedPage = {
      ...page,
      enabled
    };
    
    const updatedPages = {
      ...customPages,
      [pageId]: updatedPage
    };
    
    const updatedConfig = {
      ...config,
      customPages: updatedPages
    };
    
    updateConfig(updatedConfig);
    
    toast({
      title: enabled ? 'Page Enabled' : 'Page Disabled',
      description: `Page "${page.title}" has been ${enabled ? 'enabled' : 'disabled'}.`
    });
  };

  // Toggle page navigation visibility
  const handleTogglePageNavigation = (pageId: string, showInNavigation: boolean) => {
    const page = customPages[pageId];
    if (!page) return;
    
    const updatedPage = {
      ...page,
      showInNavigation
    };
    
    const updatedPages = {
      ...customPages,
      [pageId]: updatedPage
    };
    
    const updatedConfig = {
      ...config,
      customPages: updatedPages
    };
    
    updateConfig(updatedConfig);
    
    toast({
      title: showInNavigation ? 'Added to Navigation' : 'Removed from Navigation',
      description: `Page "${page.title}" has been ${showInNavigation ? 'added to' : 'removed from'} the navigation menu.`
    });
  };

  // Create a new page
  const handleCreatePage = () => {
    if (!newPageTitle.trim()) {
      toast({
        title: 'Validation Error',
        description: 'Page title is required',
        variant: 'destructive'
      });
      return;
    }

    // Generate slug if not provided
    const slug = newPageSlug.trim() || newPageTitle.trim()
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/(^-|-$)/g, '');

    // Check if slug already exists
    if (Object.values(customPages).some(page => page.slug === slug)) {
      toast({
        title: 'Validation Error',
        description: 'A page with this slug already exists',
        variant: 'destructive'
      });
      return;
    }

    const pageId = `page_${uuidv4().substring(0, 8)}`;
    
    const newPage: CustomPageConfig = {
      id: pageId,
      title: newPageTitle.trim(),
      slug,
      description: '',
      enabled: true,
      showInNavigation: true,
      navigationOrder: Object.values(customPages).length + 1,
      components: [
        {
          type: 'hero',
          id: `${pageId}_hero`,
          title: newPageTitle.trim(),
          subtitle: 'Page subtitle goes here',
          layout: 'center'
        },
        {
          type: 'text',
          id: `${pageId}_text`,
          title: 'Section Title',
          content: '<p>Add your content here. This is a simple text section that you can edit.</p>',
          layout: 'left'
        }
      ],
      seo: {
        title: `${newPageTitle.trim()} | ${config.siteName}`,
        description: '',
        keywords: ''
      }
    };
    
    const updatedPages = {
      ...customPages,
      [pageId]: newPage
    };
    
    const updatedConfig = {
      ...config,
      customPages: updatedPages
    };
    
    updateConfig(updatedConfig);
    
    toast({
      title: 'Page Created',
      description: `Page "${newPageTitle}" has been created successfully.`
    });
    
    setNewPageTitle('');
    setNewPageSlug('');
    setIsCreatingPage(false);
    setEditingPageId(pageId);
  };

  // Delete a page
  const handleDeletePage = () => {
    if (!deletePageId) return;
    
    const page = customPages[deletePageId];
    if (!page) return;
    
    const updatedPages = { ...customPages };
    delete updatedPages[deletePageId];
    
    const updatedConfig = {
      ...config,
      customPages: updatedPages
    };
    
    updateConfig(updatedConfig);
    
    toast({
      title: 'Page Deleted',
      description: `Page "${page.title}" has been deleted.`
    });
    
    setDeletePageId(null);
  };

  // Duplicate a page
  const handleDuplicatePage = () => {
    if (!duplicatePageId) return;
    
    const page = customPages[duplicatePageId];
    if (!page) return;
    
    const newPageId = `page_${uuidv4().substring(0, 8)}`;
    const newSlug = `${page.slug}-copy`;
    
    // Check if slug already exists and append a number if needed
    let finalSlug = newSlug;
    let counter = 1;
    while (Object.values(customPages).some(p => p.slug === finalSlug)) {
      finalSlug = `${newSlug}-${counter}`;
      counter++;
    }
    
    const newPage: CustomPageConfig = {
      ...page,
      id: newPageId,
      title: `${page.title} (Copy)`,
      slug: finalSlug,
      components: page.components.map(component => ({
        ...component,
        id: `${newPageId}_${component.id.split('_').pop()}`
      })),
      seo: page.seo ? {
        ...page.seo,
        title: `${page.title} (Copy) | ${config.siteName}`
      } : undefined
    };
    
    const updatedPages = {
      ...customPages,
      [newPageId]: newPage
    };
    
    const updatedConfig = {
      ...config,
      customPages: updatedPages
    };
    
    updateConfig(updatedConfig);
    
    toast({
      title: 'Page Duplicated',
      description: `Page "${page.title}" has been duplicated.`
    });
    
    setDuplicatePageId(null);
  };

  // If editing a page, show the page editor
  if (editingPageId) {
    return <CustomPageEditor pageId={editingPageId} onBack={() => setEditingPageId(null)} />;
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">Custom Page Manager</h2>
        <Button onClick={() => setIsCreatingPage(true)}>
          <Plus className="h-4 w-4 mr-2" /> Create Page
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Custom Pages</CardTitle>
          <CardDescription>Manage all custom pages on your website</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="mb-4">
            <Label htmlFor="search-pages">Search Pages</Label>
            <Input
              id="search-pages"
              placeholder="Search by title, slug, or description..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="max-w-md"
            />
          </div>

          {sortedPages.length === 0 ? (
            <div className="text-center p-8 border border-dashed rounded-md">
              <p className="text-muted-foreground mb-4">
                {searchTerm ? 'No pages match your search' : 'No custom pages have been created yet'}
              </p>
              <Button onClick={() => setIsCreatingPage(true)}>Create Your First Page</Button>
            </div>
          ) : (
            <div className="border rounded-md">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-[50px]">Status</TableHead>
                    <TableHead className="cursor-pointer" onClick={() => handleSort('title')}>
                      <div className="flex items-center">
                        Title
                        {sortField === 'title' && (
                          <ArrowUpDown className="ml-2 h-4 w-4" />
                        )}
                      </div>
                    </TableHead>
                    <TableHead className="cursor-pointer" onClick={() => handleSort('slug')}>
                      <div className="flex items-center">
                        Slug
                        {sortField === 'slug' && (
                          <ArrowUpDown className="ml-2 h-4 w-4" />
                        )}
                      </div>
                    </TableHead>
                    <TableHead className="w-[100px]">Navigation</TableHead>
                    <TableHead className="cursor-pointer" onClick={() => handleSort('navigationOrder')}>
                      <div className="flex items-center">
                        Order
                        {sortField === 'navigationOrder' && (
                          <ArrowUpDown className="ml-2 h-4 w-4" />
                        )}
                      </div>
                    </TableHead>
                    <TableHead>Components</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {sortedPages.map((page) => (
                    <TableRow key={page.id}>
                      <TableCell>
                        <Switch
                          checked={page.enabled}
                          onCheckedChange={(checked) => handleTogglePageEnabled(page.id, checked)}
                        />
                      </TableCell>
                      <TableCell className="font-medium">{page.title}</TableCell>
                      <TableCell className="font-mono text-xs">{page.slug}</TableCell>
                      <TableCell>
                        <Switch
                          checked={page.showInNavigation}
                          onCheckedChange={(checked) => handleTogglePageNavigation(page.id, checked)}
                        />
                      </TableCell>
                      <TableCell>{page.navigationOrder || '-'}</TableCell>
                      <TableCell>{page.components?.length || 0} components</TableCell>
                      <TableCell className="text-right">
                        <div className="flex justify-end gap-2">
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => window.open(`/website/page/${page.slug}`, '_blank')}
                            title="View Page"
                          >
                            <ExternalLink className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => setDuplicatePageId(page.id)}
                            title="Duplicate"
                          >
                            <Copy className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => setEditingPageId(page.id)}
                            title="Edit"
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => setDeletePageId(page.id)}
                            title="Delete"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Create Page Dialog */}
      <Dialog open={isCreatingPage} onOpenChange={setIsCreatingPage}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Create New Page</DialogTitle>
            <DialogDescription>
              Create a new custom page for your website
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="page-title">Page Title</Label>
              <Input
                id="page-title"
                value={newPageTitle}
                onChange={(e) => setNewPageTitle(e.target.value)}
                placeholder="About Our Team"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="page-slug">
                Page Slug (URL)
                <span className="text-xs text-muted-foreground ml-2">Optional</span>
              </Label>
              <Input
                id="page-slug"
                value={newPageSlug}
                onChange={(e) => setNewPageSlug(e.target.value)}
                placeholder="about-team"
              />
              <p className="text-xs text-muted-foreground">
                Leave blank to generate automatically from title
              </p>
            </div>
          </div>
          <DialogFooter>
            <DialogClose asChild>
              <Button variant="outline">Cancel</Button>
            </DialogClose>
            <Button onClick={handleCreatePage}>Create Page</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Page Confirmation */}
      <AlertDialog open={!!deletePageId} onOpenChange={(open) => !open && setDeletePageId(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This will permanently delete the page. This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleDeletePage} className="bg-destructive text-destructive-foreground">
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Duplicate Page Confirmation */}
      <AlertDialog open={!!duplicatePageId} onOpenChange={(open) => !open && setDuplicatePageId(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Duplicate Page</AlertDialogTitle>
            <AlertDialogDescription>
              This will create a copy of the page with all its components and settings.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleDuplicatePage}>
              Duplicate
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};

export default CustomPageManager;
