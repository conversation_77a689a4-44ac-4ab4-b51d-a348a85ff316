import React, { createContext, useContext, useEffect, useState } from 'react';
import { useConfig } from '@/config/ConfigContext';

type Theme = 'light' | 'dark' | 'blue' | 'green' | 'purple' | 'orange';

interface ThemeContextType {
  theme: Theme;
  setTheme: (theme: Theme) => void;
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

export const ThemeProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { config } = useConfig();
  const [theme, setTheme] = useState<Theme>('light');

  // Initialize theme from config when component mounts
  useEffect(() => {
    if (config.theme) {
      setTheme(config.theme as Theme);
      document.documentElement.setAttribute('data-theme', config.theme);
    }
  }, [config.theme]);

  // Update the HTML attribute when theme changes
  const handleThemeChange = (newTheme: Theme) => {
    setTheme(newTheme);
    document.documentElement.setAttribute('data-theme', newTheme);
  };

  return (
    <ThemeContext.Provider value={{ theme, setTheme: handleThemeChange }}>
      {children}
    </ThemeContext.Provider>
  );
};

export const useTheme = (): ThemeContextType => {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};
