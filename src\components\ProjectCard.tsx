
import { Card, CardContent, CardFooter } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Link } from 'react-router-dom';
import { MapPin } from 'lucide-react';

export interface ProjectProps {
  id: string;
  title: string;
  location: string;
  description: string;
  imageUrl: string;
  status: 'Upcoming' | 'Ongoing' | 'Completed';
  units: number;
  completionDate: string;
}

const ProjectCard = ({
  id,
  title,
  location,
  description,
  imageUrl,
  status,
  units,
  completionDate,
}: ProjectProps) => {
  return (
    <Card className="overflow-hidden h-full transition-transform duration-300 hover:shadow-lg">
      <div className="relative">
        <Link to={`/website/projects/${id}`}>
          <img
            src={imageUrl}
            alt={title}
            className="w-full h-64 object-cover"
          />
        </Link>
        <Badge
          className={`absolute top-3 right-3 ${
            status === 'Upcoming' 
              ? 'bg-blue-500' 
              : status === 'Ongoing' 
                ? 'bg-primary' 
                : 'bg-green-600'
          }`}
        >
          {status}
        </Badge>
      </div>
      
      <CardContent className="pt-4">
        <Link to={`/website/projects/${id}`}>
          <h3 className="text-xl font-semibold line-clamp-1 hover:text-primary transition-colors">
            {title}
          </h3>
        </Link>
        <div className="flex items-center mt-1 text-gray-600">
          <MapPin size={16} className="mr-1" />
          <span className="text-sm">{location}</span>
        </div>
        
        <p className="mt-3 text-sm text-gray-600 line-clamp-2">
          {description}
        </p>
        
        <div className="mt-3 grid grid-cols-2 gap-2 text-sm">
          <div className="text-gray-600">
            <span className="font-medium">Units:</span> {units}
          </div>
          <div className="text-gray-600">
            <span className="font-medium">Completion:</span> {completionDate}
          </div>
        </div>
      </CardContent>
      
      <CardFooter className="pt-0">
        <Link 
          to={`/website/projects/${id}`} 
          className="text-primary font-medium text-sm hover:underline"
        >
          View Project
        </Link>
      </CardFooter>
    </Card>
  );
};

export default ProjectCard;
