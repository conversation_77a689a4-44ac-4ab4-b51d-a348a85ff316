import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { 
  Image, 
  Type, 
  Layout, 
  Link, 
  ChevronDown,
  X
} from 'lucide-react';

interface ComponentTypeSelectorProps {
  onSelect: (type: string) => void;
  onCancel: () => void;
}

interface ComponentTypeOption {
  type: string;
  title: string;
  description: string;
  icon: React.ReactNode;
}

const ComponentTypeSelector: React.FC<ComponentTypeSelectorProps> = ({ onSelect, onCancel }) => {
  const componentTypes: ComponentTypeOption[] = [
    {
      type: 'hero',
      title: 'Hero Section',
      description: 'A large banner section, typically used at the top of a page',
      icon: <Image className="h-6 w-6" />
    },
    {
      type: 'text',
      title: 'Text Section',
      description: 'A section with rich text content',
      icon: <Type className="h-6 w-6" />
    },
    {
      type: 'features',
      title: 'Features Grid',
      description: 'A grid of features or services with icons',
      icon: <Layout className="h-6 w-6" />
    },
    {
      type: 'cta',
      title: 'Call to Action',
      description: 'A section with a prominent button to encourage user action',
      icon: <Link className="h-6 w-6" />
    },
    {
      type: 'accordion',
      title: 'Accordion',
      description: 'Expandable sections for FAQs or other content',
      icon: <ChevronDown className="h-6 w-6" />
    }
  ];

  return (
    <Card className="mt-6 border-dashed">
      <CardHeader>
        <div className="flex justify-between items-center">
          <CardTitle>Select Component Type</CardTitle>
          <Button variant="ghost" size="icon" onClick={onCancel}>
            <X className="h-4 w-4" />
          </Button>
        </div>
        <CardDescription>Choose the type of component to add to your page</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {componentTypes.map((option) => (
            <Button
              key={option.type}
              variant="outline"
              className="h-auto flex flex-col items-center justify-center p-6 gap-2 text-center hover:bg-muted"
              onClick={() => onSelect(option.type)}
            >
              <div className="bg-primary/10 p-3 rounded-full text-primary">
                {option.icon}
              </div>
              <div className="font-medium">{option.title}</div>
              <div className="text-xs text-muted-foreground">{option.description}</div>
            </Button>
          ))}
        </div>
      </CardContent>
      <CardFooter className="flex justify-end">
        <Button variant="outline" onClick={onCancel}>Cancel</Button>
      </CardFooter>
    </Card>
  );
};

export default ComponentTypeSelector;
