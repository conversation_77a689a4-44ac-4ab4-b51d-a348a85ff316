import { ApiConfig } from '@/config/types';
import { get } from 'lodash';

// Cache for API responses
interface CacheItem {
  data: any;
  timestamp: number;
}

const apiCache: Record<string, CacheItem> = {};

/**
 * Fetches data from an API based on the provided configuration
 * @param apiConfig The API configuration
 * @returns Transformed data according to the response mapping
 */
export const fetchFromApi = async <T>(apiConfig: ApiConfig): Promise<T[]> => {
  if (!apiConfig.enabled || !apiConfig.url) {
    throw new Error('API is not enabled or URL is not provided');
  }

  // Check cache
  const cacheKey = `${apiConfig.url}-${JSON.stringify(apiConfig.bodyParams || {})}`;
  const cacheDurationMs = (apiConfig.cacheDuration || 30) * 60 * 1000; // Convert minutes to milliseconds
  const cachedItem = apiCache[cacheKey];
  
  if (cachedItem && Date.now() - cachedItem.timestamp < cacheDurationMs) {
    console.log('Using cached data for:', apiConfig.url);
    return cachedItem.data;
  }

  try {
    // Prepare request options
    const options: RequestInit = {
      method: apiConfig.method,
      headers: apiConfig.headers ? { ...apiConfig.headers } : undefined,
    };

    // Add body for POST requests
    if (apiConfig.method === 'POST' && apiConfig.bodyParams) {
      options.body = JSON.stringify(apiConfig.bodyParams);
    }

    // Make the request
    const response = await fetch(apiConfig.url, options);
    
    if (!response.ok) {
      throw new Error(`API request failed with status ${response.status}: ${response.statusText}`);
    }

    const responseData = await response.json();
    
    // Extract items array using the provided path
    const items = get(responseData, apiConfig.responseMapping.items, []);
    
    if (!Array.isArray(items)) {
      throw new Error(`Expected array at path "${apiConfig.responseMapping.items}" but got ${typeof items}`);
    }

    // Transform data according to the mapping
    const transformedData = items.map(item => {
      const result: Record<string, any> = {};
      
      // Apply each mapping
      Object.entries(apiConfig.responseMapping).forEach(([targetKey, sourcePath]) => {
        if (targetKey !== 'items' && sourcePath) {
          result[targetKey] = get(item, sourcePath);
        }
      });
      
      return result;
    }) as T[];

    // Cache the result
    apiCache[cacheKey] = {
      data: transformedData,
      timestamp: Date.now()
    };

    return transformedData;
  } catch (error) {
    console.error('Error fetching data from API:', error);
    throw error;
  }
};

/**
 * Clears the API cache
 */
export const clearApiCache = (): void => {
  Object.keys(apiCache).forEach(key => {
    delete apiCache[key];
  });
};

/**
 * Clears a specific API cache entry
 * @param url The API URL to clear from cache
 */
export const clearApiCacheForUrl = (url: string): void => {
  Object.keys(apiCache).forEach(key => {
    if (key.startsWith(url)) {
      delete apiCache[key];
    }
  });
};
