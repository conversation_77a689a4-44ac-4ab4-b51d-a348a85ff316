
import { useP<PERSON><PERSON>, Link, Navigate } from 'react-router-dom';
import Layout from '@/components/Layout';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { MapPin, Loader2 } from 'lucide-react';
import Section from '@/components/Section';
import InquiryForm from '@/components/InquiryForm';
import { usePropertyById, useProperties } from '@/data/dataService';

const PropertyDetail = () => {
  const { id } = useParams<{ id: string }>();
  const { property, loading, error } = usePropertyById(id || '');
  const { properties } = useProperties();

  if (loading) {
    return (
      <Layout>
        <div className="container mx-auto py-16 text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p className="text-gray-500">Loading property details...</p>
        </div>
      </Layout>
    );
  }

  if (error || !property) {
    return <Navigate to="/website/properties" replace />;
  }

  const propertyFeatures = [
    { name: 'Type', value: 'Residential' },
    { name: 'Year Built', value: '2022' },
    { name: 'Heating', value: 'Central' },
    { name: 'Cooling', value: 'Central AC' },
    { name: 'Parking', value: '2-Car Garage' },
    { name: 'Lot Size', value: '0.25 acres' },
  ];

  // Get 3 related properties (excluding current one)
  const relatedProperties = properties
    .filter((p) => p.id !== property.id)
    .slice(0, 3);

  return (
    <Layout>
      <div className="pt-16">
        {/* Property Images */}
        <div className="relative h-[50vh] bg-gray-100">
          <img
            src={property.imageUrl}
            alt={property.title}
            className="w-full h-full object-cover"
          />
        </div>

        <div className="container mx-auto px-4 py-8">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {/* Property Details */}
            <div className="md:col-span-2">
              <div className="mb-6">
                <div className="flex items-center justify-between">
                  <Badge
                    className={`${
                      property.status === 'For Sale'
                        ? 'bg-primary'
                        : property.status === 'For Rent'
                        ? 'bg-blue-500'
                        : 'bg-gray-500'
                    }`}
                  >
                    {property.status}
                  </Badge>
                  <div className="text-gray-500">Property ID: {property.id}</div>
                </div>
                <h1 className="text-3xl md:text-4xl font-bold mt-2 mb-2">
                  {property.title}
                </h1>
                <div className="flex items-center text-gray-600 mb-4">
                  <MapPin size={18} className="mr-1" />
                  <span>{property.location}</span>
                </div>
                <p className="text-2xl font-bold text-primary mb-4">
                  {property.price}
                </p>

                <div className="grid grid-cols-3 gap-4 py-4 border-y border-gray-200">
                  <div className="text-center">
                    <p className="text-gray-500">Bedrooms</p>
                    <p className="font-semibold text-lg">{property.beds}</p>
                  </div>
                  <div className="text-center">
                    <p className="text-gray-500">Bathrooms</p>
                    <p className="font-semibold text-lg">{property.baths}</p>
                  </div>
                  <div className="text-center">
                    <p className="text-gray-500">Area</p>
                    <p className="font-semibold text-lg">{property.sqft} sq ft</p>
                  </div>
                </div>
              </div>

              <div className="mb-8">
                <h2 className="text-2xl font-semibold mb-4">Description</h2>
                <p className="text-gray-600 mb-4">
                  This stunning {property.beds}-bedroom, {property.baths}-bathroom property in {property.location} offers an exceptional living experience. With {property.sqft} square feet of thoughtfully designed space, this home combines luxury with practicality.
                </p>
                <p className="text-gray-600 mb-4">
                  The property features high ceilings, abundant natural light, and premium finishes throughout. The gourmet kitchen includes high-end appliances, custom cabinetry, and a spacious island perfect for entertaining.
                </p>
                <p className="text-gray-600">
                  Outside, you'll find beautifully landscaped grounds with multiple outdoor living spaces designed for relaxation and entertainment. The location offers convenience to shopping, dining, and major transportation routes.
                </p>
              </div>

              <div className="mb-8">
                <h2 className="text-2xl font-semibold mb-4">Features & Amenities</h2>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                  {propertyFeatures.map((feature, index) => (
                    <div key={index} className="flex flex-col">
                      <span className="text-gray-500">{feature.name}</span>
                      <span className="font-medium">{feature.value}</span>
                    </div>
                  ))}
                </div>
              </div>

              <div className="mb-8">
                <h2 className="text-2xl font-semibold mb-4">Location</h2>
                <div className="bg-gray-200 h-64 flex items-center justify-center">
                  <p className="text-gray-600">Map would go here in a real application</p>
                </div>
              </div>
            </div>

            {/* Sidebar */}
            <div>
              <div className="sticky top-24">
                <InquiryForm
                  type="property"
                  title="Inquire About This Property"
                  itemName={property.title}
                />

                <div className="mt-6">
                  <Button asChild variant="outline" className="w-full">
                    <Link to="/website/properties">Back to All Properties</Link>
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Related Properties */}
        <Section
          title="Similar Properties"
          subtitle="You might also be interested in these properties"
          className="bg-gray-50"
        >
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {relatedProperties.map((relatedProperty) => (
              <div key={relatedProperty.id} className="bg-white rounded-lg shadow-sm overflow-hidden">
                <Link to={`/website/properties/${relatedProperty.id}`}>
                  <img
                    src={relatedProperty.imageUrl}
                    alt={relatedProperty.title}
                    className="w-full h-48 object-cover"
                  />
                </Link>
                <div className="p-4">
                  <Link
                    to={`/properties/${relatedProperty.id}`}
                    className="text-lg font-semibold hover:text-primary transition"
                  >
                    {relatedProperty.title}
                  </Link>
                  <p className="text-gray-600 mt-1">{relatedProperty.location}</p>
                  <p className="text-primary font-bold mt-2">{relatedProperty.price}</p>
                </div>
              </div>
            ))}
          </div>
        </Section>
      </div>
    </Layout>
  );
};

export default PropertyDetail;
