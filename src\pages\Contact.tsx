
import { useState } from 'react';
import Layout from '@/components/Layout';
import PageHeader from '@/components/PageHeader';
import Section from '@/components/Section';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { useToast } from '@/components/ui/use-toast';
import { Card, CardContent } from '@/components/ui/card';
import { useConfig } from '@/config/ConfigContext';

const Contact = () => {
  const { toast } = useToast();
  const { config } = useConfig();
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    subject: '',
    message: '',
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    // Simulate form submission (since it's static)
    setTimeout(() => {
      toast({
        title: 'Message Sent',
        description: 'Thank you for contacting us. We will get back to you shortly.',
      });
      setFormData({
        name: '',
        email: '',
        phone: '',
        subject: '',
        message: '',
      });
      setIsSubmitting(false);
    }, 1000);
  };

  const officeLocations = [
    {
      name: 'Main Office',
      address: '123 Real Estate Avenue, Property City, PC 12345',
      phone: '(*************',
      email: '<EMAIL>',
      hours: 'Monday - Friday: 9am - 6pm',
    },
    {
      name: 'Downtown Branch',
      address: '456 Urban Street, Downtown, DT 67890',
      phone: '(*************',
      email: '<EMAIL>',
      hours: 'Monday - Saturday: 10am - 7pm',
    },
    {
      name: 'Coastal Office',
      address: '789 Beach Road, Seaside, SS 54321',
      phone: '(*************',
      email: '<EMAIL>',
      hours: 'Monday - Friday: 9am - 5pm',
    },
  ];

  return (
    <Layout>
      <PageHeader
        title={config.contactPage.pageHeader.title}
        subtitle={config.contactPage.pageHeader.subtitle}
        backgroundImage={config.contactPage.pageHeader.backgroundImage}
      />

      <Section title={config.contactPage.contactForm.title}>
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          <div className="lg:col-span-2">
            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="name">Full Name</Label>
                  <Input
                    id="name"
                    name="name"
                    placeholder="Your name"
                    value={formData.name}
                    onChange={handleChange}
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="email">Email</Label>
                  <Input
                    id="email"
                    name="email"
                    type="email"
                    placeholder="<EMAIL>"
                    value={formData.email}
                    onChange={handleChange}
                    required
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="phone">Phone</Label>
                  <Input
                    id="phone"
                    name="phone"
                    placeholder="Your phone number"
                    value={formData.phone}
                    onChange={handleChange}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="subject">Subject</Label>
                  <Input
                    id="subject"
                    name="subject"
                    placeholder="Subject of your message"
                    value={formData.subject}
                    onChange={handleChange}
                    required
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="message">Message</Label>
                <Textarea
                  id="message"
                  name="message"
                  placeholder="Your message"
                  value={formData.message}
                  onChange={handleChange}
                  rows={6}
                  required
                />
              </div>

              <Button
                type="submit"
                className="w-full md:w-auto"
                disabled={isSubmitting}
              >
                {isSubmitting ? 'Sending...' : config.contactPage.contactForm.submitButtonText || 'Send Message'}
              </Button>
            </form>
          </div>

          <div>
            <Card>
              <CardContent className="pt-6">
                <h3 className="text-xl font-semibold mb-4">Contact Information</h3>
                <div className="space-y-4">
                  <div>
                    <p className="font-medium">Address:</p>
                    {config.contactPage.contactInfo.address.map((line, index) => (
                      <p key={index} className="text-gray-600">{line}</p>
                    ))}
                  </div>

                  <div>
                    <p className="font-medium">Phone:</p>
                    <p className="text-gray-600">{config.contactPage.contactInfo.phone}</p>
                  </div>

                  <div>
                    <p className="font-medium">Email:</p>
                    <p className="text-gray-600">{config.contactPage.contactInfo.email}</p>
                  </div>

                  <div>
                    <p className="font-medium">Office Hours:</p>
                    <p className="text-gray-600">Monday - Friday: 9am - 6pm</p>
                    <p className="text-gray-600">Saturday: 10am - 4pm</p>
                    <p className="text-gray-600">Sunday: Closed</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </Section>

      <Section title="Our Offices" className="bg-gray-50">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {officeLocations.map((office, index) => (
            <Card key={index}>
              <CardContent className="pt-6">
                <h3 className="text-xl font-semibold mb-2">{office.name}</h3>
                <div className="space-y-3">
                  <p className="text-gray-600">{office.address}</p>
                  <p className="text-gray-600">{office.phone}</p>
                  <p className="text-gray-600">{office.email}</p>
                  <p className="text-gray-600">{office.hours}</p>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </Section>

      <Section title="Location">
        <div className="bg-gray-200 h-96 flex items-center justify-center rounded-lg">
          <p className="text-gray-600">Map would go here in a real application</p>
        </div>
      </Section>
    </Layout>
  );
};

export default Contact;
