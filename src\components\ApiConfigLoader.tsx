import React, { useState } from 'react';
import { useConfig } from '@/config/ConfigContext';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { AlertCircle, CheckCircle2, Loader2 } from 'lucide-react';

const ApiConfigLoader: React.FC = () => {
  const { loadConfigFromApi, isLoading, loadingError } = useConfig();
  const [apiUrl, setApiUrl] = useState<string>('');
  const [success, setSuccess] = useState<boolean>(false);

  const handleLoadConfig = async () => {
    if (!apiUrl.trim()) {
      return;
    }

    setSuccess(false);
    
    try {
      await loadConfigFromApi(apiUrl);
      setSuccess(true);
    } catch (error) {
      // Error is already handled in the context
      console.error('Error loading config:', error);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Load Configuration from API</CardTitle>
        <CardDescription>
          Enter the URL of a JSON API endpoint that provides a valid website configuration.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="api-url">API URL</Label>
          <Input
            id="api-url"
            placeholder="https://example.com/api/config"
            value={apiUrl}
            onChange={(e) => setApiUrl(e.target.value)}
            disabled={isLoading}
          />
        </div>

        {loadingError && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>{loadingError}</AlertDescription>
          </Alert>
        )}

        {success && !loadingError && (
          <Alert variant="default" className="bg-green-50 border-green-200">
            <CheckCircle2 className="h-4 w-4 text-green-600" />
            <AlertTitle className="text-green-600">Success</AlertTitle>
            <AlertDescription className="text-green-700">
              Configuration successfully loaded from API
            </AlertDescription>
          </Alert>
        )}
      </CardContent>
      <CardFooter>
        <Button 
          onClick={handleLoadConfig} 
          disabled={isLoading || !apiUrl.trim()}
          className="w-full"
        >
          {isLoading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Loading...
            </>
          ) : (
            'Load Configuration'
          )}
        </Button>
      </CardFooter>
    </Card>
  );
};

export default ApiConfigLoader;
