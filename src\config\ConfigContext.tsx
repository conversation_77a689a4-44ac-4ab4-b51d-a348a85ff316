import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { WebsiteConfig } from './types';
import { defaultConfig } from './defaultConfig';
import { getActiveConfig, saveConfigToLocalStorage } from './configLoader';
import { configApiService } from '../services/configApiService';
import { toast } from '../components/ui/use-toast';

// Create the context
interface ConfigContextType {
  config: WebsiteConfig;
  updateConfig: (newConfig: WebsiteConfig) => void;
  resetConfig: () => void;
  saveConfig: () => void;
  loadConfigFromApi: (url: string) => Promise<void>;
  isLoading: boolean;
  loadingError: string | null;
}

const ConfigContext = createContext<ConfigContextType | undefined>(undefined);

// Create a provider component
interface ConfigProviderProps {
  children: ReactNode;
}

export const ConfigProvider: React.FC<ConfigProviderProps> = ({ children }) => {
  const [config, setConfig] = useState<WebsiteConfig>(defaultConfig);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [loadingError, setLoadingError] = useState<string | null>(null);

  // Load config on initial render
  useEffect(() => {
    const activeConfig = getActiveConfig();
    setConfig(activeConfig);
  }, []);

  // Update the config
  const updateConfig = (newConfig: WebsiteConfig) => {
    // Create a deep copy to avoid mutation issues
    const configCopy = JSON.parse(JSON.stringify(newConfig));
    console.log('Updating config:', configCopy);
    setConfig(configCopy);

    // Save to localStorage immediately
    saveConfigToLocalStorage(configCopy);
  };

  // Reset to default config
  const resetConfig = () => {
    setConfig(defaultConfig);
    localStorage.removeItem('websiteConfig');
  };

  // Save config to localStorage (mostly for backward compatibility)
  const saveConfig = () => {
    console.log('Explicitly saving config to localStorage:', config);
    saveConfigToLocalStorage(config);
  };

  // Load configuration from an API endpoint
  const loadConfigFromApi = async (url: string) => {
    setIsLoading(true);
    setLoadingError(null);

    try {
      // Fetch configuration from API
      const apiConfig = await configApiService.fetchConfig(url);

      // Validate the configuration structure
      const validation = configApiService.validateConfig(apiConfig);

      if (!validation.isValid) {
        const errorMessage = `Invalid configuration: ${validation.errors.join(', ')}`;
        setLoadingError(errorMessage);
        toast({
          title: 'Configuration Error',
          description: errorMessage,
          variant: 'destructive'
        });
        setIsLoading(false);
        return;
      }

      // Merge with default config to ensure all required properties exist
      const mergedConfig = configApiService.mergeWithDefaultConfig(apiConfig);

      // Update the configuration
      setConfig(mergedConfig);

      // Save to localStorage
      saveConfigToLocalStorage(mergedConfig);

      toast({
        title: 'Configuration Loaded',
        description: 'Successfully loaded configuration from API',
        variant: 'default'
      });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      setLoadingError(errorMessage);
      toast({
        title: 'API Error',
        description: errorMessage,
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <ConfigContext.Provider value={{
      config,
      updateConfig,
      resetConfig,
      saveConfig,
      loadConfigFromApi,
      isLoading,
      loadingError
    }}>
      {children}
    </ConfigContext.Provider>
  );
};

// Create a hook for using the config context
export const useConfig = (): ConfigContextType => {
  const context = useContext(ConfigContext);
  if (context === undefined) {
    throw new Error('useConfig must be used within a ConfigProvider');
  }
  return context;
};
